<?php

namespace App\Services;

use App\Models\User;
use App\Models\LoginAttempt;
use App\Models\UserLoginLog;
use Illuminate\Support\Facades\Hash;

class LoginSecurityService
{
    /**
     * Attempt to authenticate user with security checks
     */
    public function attemptLogin(string $email, string $password, string $ip, ?string $userAgent = null): array
    {
        // Check if email/IP combination is locked or deactivated
        $loginAttempt = LoginAttempt::where('email', $email)
            ->where('ip_address', $ip)
            ->first();

        // IP-based security check
        if ($loginAttempt) {
            if ($loginAttempt->isDeactivated()) {
                return [
                    'success' => false,
                    'message' => 'Your account has been deactivated due to multiple failed login attempts. Please contact administrators.',
                    'type' => 'deactivated'
                ];
            }

            if ($loginAttempt->isLocked()) {
                return [
                    'success' => false,
                    'message' => $loginAttempt->lockout_message,
                    'type' => 'locked'
                ];
            }
        }

        // Find user by email
        $user = User::where('email', $email)->first();

        // User-based security check (account-wide)
        if ($user) {
            if ($user->isDeactivated()) {
                return [
                    'success' => false,
                    'message' => 'Your account has been deactivated. Please contact administrators.',
                    'type' => 'user_deactivated'
                ];
            }

            if ($user->isLocked()) {
                return [
                    'success' => false,
                    'message' => 'Your account is temporarily locked. Try again in ' . $user->getRemainingLockoutMinutes() . ' minutes.',
                    'type' => 'user_locked'
                ];
            }
        }

        if (!$user) {
            // User doesn't exist - still record attempt to prevent enumeration
            $this->recordFailedAttempt($email, $ip, $userAgent, 'User not found', null);
            return [
                'success' => false,
                'message' => 'Invalid email or password.',
                'type' => 'invalid_credentials'
            ];
        }

        // Check password
        if (!Hash::check($password, $user->password)) {
            $this->recordFailedAttempt($email, $ip, $userAgent, 'Invalid password', $user);
            
            // Get updated attempt record to check if lockout was applied
            $updatedAttempt = LoginAttempt::where('email', $email)
                ->where('ip_address', $ip)
                ->first();

            if ($updatedAttempt && $updatedAttempt->isLocked()) {
                return [
                    'success' => false,
                    'message' => $updatedAttempt->lockout_message,
                    'type' => 'locked_after_failed'
                ];
            }

            if ($updatedAttempt && $updatedAttempt->isDeactivated()) {
                // Also deactivate the user account
                $user->deactivateAccount('Multiple failed login attempts');
                
                return [
                    'success' => false,
                    'message' => 'Your account has been deactivated due to multiple failed login attempts. Please contact administrators.',
                    'type' => 'deactivated_after_failed'
                ];
            }

            return [
                'success' => false,
                'message' => 'Invalid email or password.',
                'type' => 'invalid_credentials'
            ];
        }

        // Successful login
        $this->recordSuccessfulLogin($user, $ip, $userAgent);
        
        return [
            'success' => true,
            'user' => $user,
            'message' => 'Login successful.'
        ];
    }

    /**
     * Record a failed login attempt
     */
    private function recordFailedAttempt(string $email, string $ip, ?string $userAgent, string $reason, ?User $user): void
    {
        // Record in login attempts table (for lockout tracking)
        LoginAttempt::recordFailedAttempt($email, $ip);

        // Record in user if exists
        if ($user) {
            $user->recordFailedLogin($reason);
        }

        // Log the attempt
        $this->logFailedAttempt($email, $ip, $userAgent, $reason, $user);
    }

    /**
     * Record a successful login
     */
    private function recordSuccessfulLogin(User $user, string $ip, ?string $userAgent): void
    {
        // Clear any existing login attempts for this email/IP
        LoginAttempt::where('email', $user->email)
            ->where('ip_address', $ip)
            ->delete();

        // Record successful login on user
        $user->recordSuccessfulLogin($ip, $userAgent);
    }

    /**
     * Log failed attempt
     */
    private function logFailedAttempt(string $email, string $ip, ?string $userAgent, string $reason, ?User $user): void
    {
        UserLoginLog::logAttempt(
            $user,
            $email,
            'failed',
            $ip,
            $userAgent,
            null,
            $reason
        );
    }

    /**
     * Get login attempt status for email/IP combination
     */
    public function getLoginAttemptStatus(string $email, string $ip): ?LoginAttempt
    {
        $attempt = LoginAttempt::where('email', $email)
            ->where('ip_address', $ip)
            ->first();

        if ($attempt) {
            $attempt->checkAndClearExpiredLockout();
        }

        return $attempt;
    }

    /**
     * Clear login attempts for user (admin action)
     */
    public function clearLoginAttempts(string $email): void
    {
        LoginAttempt::where('email', $email)->delete();
        
        $user = User::where('email', $email)->first();
        if ($user) {
            $user->unlockAccount();
        }
    }

    /**
     * Reactivate deactivated account (admin action)
     */
    public function reactivateAccount(string $email): bool
    {
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            return false;
        }

        $user->reactivateAccount();
        
        // Clear all login attempts
        LoginAttempt::where('email', $email)->delete();

        // Log the reactivation
        UserLoginLog::logAttempt(
            $user,
            $email,
            'success',
            request()->ip(),
            request()->userAgent(),
            null,
            'Account reactivated by administrator'
        );

        return true;
    }

    /**
     * Get recent login logs for user
     */
    public function getRecentLoginLogs(User $user, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return $user->loginLogs()
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get failed login statistics
     */
    public function getFailedLoginStats(int $hours = 24): array
    {
        $since = now()->subHours($hours);

        return [
            'total_failed' => UserLoginLog::failed()->where('created_at', '>=', $since)->count(),
            'unique_emails' => UserLoginLog::failed()->where('created_at', '>=', $since)->distinct('email')->count(),
            'unique_ips' => UserLoginLog::failed()->where('created_at', '>=', $since)->distinct('ip_address')->count(),
            'locked_accounts' => LoginAttempt::locked()->count(),
            'deactivated_accounts' => LoginAttempt::deactivated()->count(),
        ];
    }
}

