<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class NewsApiService
{
    protected $apiUrl;
    protected $apiKey;
    protected $timeout;

    public function __construct()
    {
        $this->apiUrl = config('services.news_api.url');
        $this->apiKey = config('services.news_api.key');
        $this->timeout = config('services.news_api.timeout', 30);
    }

    /**
     * Fetch latest news from external API
     *
     * @return array
     * @throws Exception
     */
    public function fetchLatestNews(array $params = [])
    {
        try {
            // Log the API request
            Log::info('NewsApiService: Fetching news from external API', [
                'params' => $params,
                'api_url' => $this->apiUrl
            ]);

            // Build query parameters
            $queryParams = [];

            // Add parameters if they exist (keep original parameter names for custom API)
            if (isset($params['limit'])) $queryParams['limit'] = $params['limit'];
            if (isset($params['country'])) $queryParams['country'] = $params['country'];
            if (isset($params['language'])) $queryParams['language'] = $params['language'];
            if (isset($params['category'])) $queryParams['category'] = $params['category'];

            // Make API request using Laravel HTTP client
            $response = Http::timeout($this->timeout)
                ->withHeaders([
                    'X-API-Key' => $this->apiKey,
                    'Accept' => 'application/json',
                ])
                ->get($this->apiUrl, $queryParams);

            if (!$response->successful()) {
                Log::error('NewsApiService: API request failed', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return [];
            }

            $data = $response->json();
            // Log successful response
            Log::info('NewsApiService: API response received', [
                'response_keys' => array_keys($data),
                'data_structure' => is_array($data) ? 'array' : gettype($data)
            ]);

            // Use existing method to handle different API response formats
            $articles = $this->normalizeApiResponse($data);

            Log::info('NewsApiService: Articles extracted', [
                'articles_count' => count($articles)
            ]);

            // Process and return articles using existing method
            return $this->processArticles($articles);
        } catch (\Exception $e) {
            Log::error('NewsApiService: Exception occurred', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }



    /**
     * Process and normalize articles from API response
     *
     * @param array $articles
     * @return array
     */
    protected function processArticles(array $articles): array
    {
        $processedArticles = [];

        foreach ($articles as $article) {
            $processed = $this->normalizeArticle($article);

            if ($this->isValidArticle($processed)) {
                $processedArticles[] = $processed;
            }
        }

        Log::info('NewsApiService: Articles processed', [
            'original_count' => count($articles),
            'processed_count' => count($processedArticles)
        ]);

        return $processedArticles;
    }

    /**
     * Normalize article data to consistent format
     *
     * @param array $article
     * @return array
     */
    protected function normalizeArticle(array $article): array
    {
        return [
            'title' => $this->extractTitle($article),
            'description' => $article['description'] ?? null,
            'source_url' => $article['url'] ?? null,
            'source_published_at' => $this->extractPublishedDate($article),
            'published_by' => $article['source']['name'] ?? 'Unknown Source',
            'category' => 'General', // Default category
            'api_response' => $article, // Store original response
        ];
    }

    /**
     * Extract title from article data
     */
    protected function extractTitle(array $article): ?string
    {
        $title = $article['title'] ?? $article['headline'] ?? $article['name'] ?? null;

        // Clean up title (remove source attribution that some APIs add)
        if ($title && strpos($title, ' - ') !== false) {
            $parts = explode(' - ', $title);
            if (count($parts) > 1) {
                // Take the first part as the main title
                $title = trim($parts[0]);
            }
        }

        return $title;
    }





    /**
     * Extract published date from article data
     */
    protected function extractPublishedDate(array $article): ?string
    {
        // NewsAPI typically uses 'publishedAt' field
        $dateField = $article['publishedAt'] ??
                    $article['published_at'] ??
                    $article['published'] ??
                    $article['date'] ??
                    $article['created_at'] ??
                    null;

        if ($dateField) {
            try {
                // Convert to MySQL datetime format
                $timestamp = strtotime($dateField);
                if ($timestamp !== false) {
                    return date('Y-m-d H:i:s', $timestamp);
                }
            } catch (Exception $e) {
                Log::warning('NewsApiService: Failed to parse date', [
                    'date' => $dateField,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Return current timestamp as fallback
        return now()->format('Y-m-d H:i:s');
    }



    /**
     * Validate if article has required fields
     */
    protected function isValidArticle(array $article): bool
    {
        return !empty($article['title']);
    }

    /**
     * Handle different API response formats
     */
    protected function normalizeApiResponse(array $data): array
    {
        // If data is directly an array of articles
        if (isset($data[0]) && is_array($data[0])) {
            return $data;
        }

        // Handle your specific API format with top_stories
        if (isset($data['top_stories']) && is_array($data['top_stories'])) {
            return $data['top_stories'];
        }

        // If data has a different structure, try to extract articles
        foreach (['items', 'results', 'posts', 'news', 'articles'] as $key) {
            if (isset($data[$key]) && is_array($data[$key])) {
                return $data[$key];
            }
        }

        return [];
    }


}

