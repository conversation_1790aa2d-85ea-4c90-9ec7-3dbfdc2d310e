<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use App\Helpers\ViewHelpers;
use Illuminate\Support\Facades\View;

class HelperServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Make the getRandomCategoryColor function available in Blade templates
        Blade::directive('randomCategoryColor', function () {
            return "<?php \$categoryColor = \App\Helpers\ViewHelpers::getRandomCategoryColor(); ?>";
        });

        View::share('topCategories', \App\Helpers\ViewHelpers::getTopCategories());
    }
}
