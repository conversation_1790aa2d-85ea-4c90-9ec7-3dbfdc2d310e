<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - @yield('title', 'Authentication')</title>

    <!-- Roboto font is now loaded locally via CSS @font-face declarations -->

    <!-- Material Icons - Round (thin) version -->
    <link href="https://fonts.googleapis.com/css2?family=Material+Icons+Round" rel="stylesheet">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <style>
        body {
            font-family: 'Roboto', sans-serif;
        }
        .material-icons-round {
            font-family: 'Material Icons Round';
            font-weight: normal;
            font-style: normal;
            font-size: 24px;
            line-height: 1;
            letter-spacing: normal;
            text-transform: none;
            display: inline-block;
            white-space: nowrap;
            word-wrap: normal;
            direction: ltr;
            -webkit-font-smoothing: antialiased;
        }
        .material-input-with-icon {
            @apply block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:border-gray-400 sm:text-sm;
        }
        .material-card {
            @apply bg-white shadow-md rounded-lg p-6;
        }
    </style>
    
    @stack('styles')
</head>
<body class="bg-gray-50 font-sans antialiased">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="w-full max-w-md">
            <!-- Logo and Header -->
            <div class="text-center mb-8">
                <div class="flex justify-center mb-6">
                    <div class="w-16 h-16 bg-blue-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <span class="text-white font-bold text-2xl">L</span>
                    </div>
                </div>
                <h2 class="text-3xl font-bold text-gray-900">@yield('header', 'Authentication')</h2>
                <p class="mt-2 text-sm text-gray-600">
                    @yield('subheader', '')
                </p>
            </div>

            <!-- Card Container -->
            <div class="bg-white py-8 px-6 shadow-md rounded-lg">
                <!-- Success/Error Messages -->
                @if(session('success'))
                    <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                        <div class="flex items-center">
                            <i class="material-icons-round mr-2">check_circle</i>
                            <span>{{ session('success') }}</span>
                        </div>
                    </div>
                @endif

                @if(session('status'))
                    <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                        <div class="flex items-center">
                            <i class="material-icons-round mr-2">check_circle</i>
                            <span>{{ session('status') }}</span>
                        </div>
                    </div>
                @endif

                {{-- Removed global error display to prevent duplicate messages --}}
                {{-- Individual field errors are handled in each form --}}

                <!-- Main Content -->
                @yield('content')
            </div>
        </div>
    </div>

    @stack('scripts')
</body>
</html>
