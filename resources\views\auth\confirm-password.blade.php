@extends('layouts.admin')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden">
        <div class="px-6 py-4">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Confirm Password</h2>
            <p class="text-gray-600 mb-4">This is a secure area of the application. Please confirm your password before continuing.</p>

            <form method="POST" action="{{ url('/confirm-password') }}">
                @csrf

                <div class="mb-4">
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="material-icons text-gray-400 text-lg">lock</i>
                        </div>
                        <input id="password" type="password" name="password" required autocomplete="current-password" 
                               class="material-input-with-icons @error('password') border-red-500 @enderror">
                        
                        <button type="button" 
                                id="toggle-password" 
                                data-password-toggle="password:password-icon"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="material-icons text-gray-400 hover:text-gray-600 text-lg" id="password-icon">visibility</i>
                        </button>
                    </div>
                    
                    @error('password')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div class="flex justify-end">
                    <button type="submit" class="btn-primary">
                        Confirm
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // document.addEventListener('DOMContentLoaded', function() {
    //     // Password toggle functionality
    //     const togglePasswordBtn = document.getElementById('toggle-password');
    //     const passwordInput = document.getElementById('password');
    //     const passwordIcon = document.getElementById('password-icon');

    //     if (togglePasswordBtn && passwordInput && passwordIcon) {
    //         togglePasswordBtn.addEventListener('click', function() {
    //             const isPassword = passwordInput.type === 'password';
    //             passwordInput.type = isPassword ? 'text' : 'password';
    //             passwordIcon.textContent = isPassword ? 'visibility_off' : 'visibility';
    //         });
    //     }
    // });
</script>
@endpush
@endsection
