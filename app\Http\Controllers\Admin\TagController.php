<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Yajra\DataTables\Facades\DataTables;
use App\Helpers\DataTableHelper;

class TagController extends Controller
{
    /**
     * Display a listing of tags and handle datatable requests.
     */
    public function index(Request $request)
    {
        // Check if this is an AJAX request for datatable
        if ($request->ajax()) {
            $query = Tag::query();

            return DataTables::of($query)
                ->addColumn('tag', function ($tag) {
                    return '<div class="flex items-center">
                        <div class="w-4 h-4 rounded-full mr-3 border border-gray-200" style="background-color: ' . $tag->color_with_opacity . ';"></div>
                        <div>
                            <div class="font-medium text-gray-900">' . e($tag->name) . '</div>
                            <div class="text-sm text-gray-500">' . e($tag->slug) . '</div>
                        </div>
                    </div>';
                })
                ->addColumn('status', function ($tag) {
                    return DataTableHelper::statusBadge($tag->status);
                })
                ->addColumn('actions', function ($tag) {
                    return DataTableHelper::actionsColumn($tag, 'admin.tags', [
                        'viewTitle' => 'View Tag',
                        'editTitle' => 'Edit Tag',
                        'deleteTitle' => 'Delete Tag'
                    ]);
                })
                ->editColumn('created_at', function ($tag) {
                    return $tag->created_at->format('M d, Y');
                })
                ->rawColumns(['tag', 'status', 'actions'])
                ->make(true);
        }
        
        return view('admin.tags.index');
    }

    /**
     * Show the form for creating a new tag.
     */
    public function create()
    {
        return view('admin.tags.create');
    }

    /**
     * Store a newly created tag in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'slug' => ['nullable', 'string', 'max:255', 'unique:tags,slug'],
            'description' => ['nullable', 'string'],
            'color' => ['required', 'string', 'regex:/^#[0-9A-Fa-f]{6}$/'],
            'status' => ['required', 'in:active,inactive'],
            'sort_order' => ['nullable', 'integer', 'min:0'],
            'meta_title' => ['nullable', 'string', 'max:255'],
            'meta_description' => ['nullable', 'string', 'max:500'],
            'meta_keywords' => ['nullable', 'string'],
            'canonical_url' => ['nullable', 'url'],
            'robots_meta' => ['nullable', 'string'],
            'og_title' => ['nullable', 'string', 'max:255'],
            'og_description' => ['nullable', 'string', 'max:500'],
            'og_image' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
            'og_type' => ['nullable', 'string'],
        ]);

        $tagData = $request->except(['og_image']);
        
        // Generate slug if not provided
        if (empty($tagData['slug'])) {
            $tagData['slug'] = Str::slug($tagData['name']);
        }

        // Handle OG image upload
        if ($request->hasFile('og_image')) {
            $ogImagePath = $request->file('og_image')->store('tags/og-images', 'public');
            $tagData['og_image'] = $ogImagePath;
        }

        Tag::create($tagData);
        
        return redirect()->route('admin.tags.index')->with('success', 'Tag created successfully.');
    }

    /**
     * Display the specified tag.
     */
    public function show(Tag $tag)
    {
        return view('admin.tags.show', compact('tag'));
    }

    /**
     * Show the form for editing the specified tag.
     */
    public function edit(Tag $tag)
    {
        return view('admin.tags.edit', compact('tag'));
    }

    /**
     * Update the specified tag in storage.
     */
    public function update(Request $request, Tag $tag)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'slug' => ['nullable', 'string', 'max:255', 'unique:tags,slug,' . $tag->id],
            'description' => ['nullable', 'string'],
            'color' => ['required', 'string', 'regex:/^#[0-9A-Fa-f]{6}$/'],
            'status' => ['required', 'in:active,inactive'],
            'sort_order' => ['nullable', 'integer', 'min:0'],
            'meta_title' => ['nullable', 'string', 'max:255'],
            'meta_description' => ['nullable', 'string', 'max:500'],
            'meta_keywords' => ['nullable', 'string'],
            'canonical_url' => ['nullable', 'url'],
            'robots_meta' => ['nullable', 'string'],
            'og_title' => ['nullable', 'string', 'max:255'],
            'og_description' => ['nullable', 'string', 'max:500'],
            'og_image' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
            'og_type' => ['nullable', 'string'],
            'remove_og_image' => ['boolean'],
        ]);

        $tagData = $request->except(['og_image', 'remove_og_image']);
        
        // Generate slug if not provided
        if (empty($tagData['slug'])) {
            $tagData['slug'] = Str::slug($tagData['name']);
        }

        // Handle OG image removal
        if ($request->boolean('remove_og_image') && $tag->og_image) {
            Storage::disk('public')->delete($tag->og_image);
            $tagData['og_image'] = null;
        }

        // Handle OG image upload
        if ($request->hasFile('og_image')) {
            // Delete old image if exists
            if ($tag->og_image) {
                Storage::disk('public')->delete($tag->og_image);
            }
            
            $ogImagePath = $request->file('og_image')->store('tags/og-images', 'public');
            $tagData['og_image'] = $ogImagePath;
        }

        $tag->update($tagData);
        
        return redirect()->route('admin.tags.index')->with('success', 'Tag updated successfully.');
    }

    /**
     * Remove the specified tag from storage.
     */
    public function destroy(Tag $tag)
    {
        // Delete OG image if exists
        if ($tag->og_image) {
            Storage::disk('public')->delete($tag->og_image);
        }

        $tag->delete();
        
        return response()->json(['success' => true, 'message' => 'Tag deleted successfully.']);
    }
}
