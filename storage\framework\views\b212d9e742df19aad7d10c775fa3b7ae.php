<?php $__env->startSection('title', 'My Profile'); ?>

<?php
    $pageTitle = 'My Profile';
    $pageDescription = 'View and manage your profile information';
    $breadcrumbs = [
        ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
        ['title' => 'My Profile', 'url' => '#']
    ];
?>

<?php $__env->startSection('page-header'); ?>
<div class="flex items-center justify-between">
    <div>
        <h1 class="text-3xl font-semibold text-gray-900"><?php echo e($pageTitle); ?></h1>
        <p class="mt-2 text-gray-600"><?php echo e($pageDescription); ?></p>
    </div>
    <div class="flex space-x-3">
        <a href="<?php echo e(route('admin.profile.edit')); ?>" class="material-button material-button-primary flex items-center">
            <i class="material-icons mr-2">edit</i>
            Edit Profile
        </a>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- User Profile Card -->
    <div class="lg:col-span-1">
        <div class="material-card p-6">
            <div class="text-center">
                <?php if($user->profile_image): ?>
                    <img src="<?php echo e(asset('storage/' . $user->profile_image)); ?>" 
                         alt="<?php echo e($user->name); ?>" 
                         class="w-24 h-24 rounded-full object-cover mx-auto mb-4">
                <?php else: ?>
                    <div class="w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-white font-bold text-2xl"><?php echo e(substr($user->name, 0, 1)); ?></span>
                    </div>
                <?php endif; ?>
                <h3 class="text-xl font-semibold text-gray-900"><?php echo e($user->name); ?></h3>
                <p class="text-gray-600"><?php echo e($user->email); ?></p>
                
                <!-- Status Badges -->
                <div class="flex justify-center space-x-2 mt-4">
                    <?php if($user->email_verified_at): ?>
                        <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            <i class="material-icons mr-1 text-xs">verified</i>
                            Verified
                        </span>
                    <?php else: ?>
                        <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                            <i class="material-icons mr-1 text-xs">pending</i>
                            Unverified
                        </span>
                    <?php endif; ?>
                    
                    <?php if($user->roles->isNotEmpty()): ?>
                        <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                            <i class="material-icons mr-1 text-xs">admin_panel_settings</i>
                            <?php echo e($user->roles->first()->name); ?>

                        </span>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="mt-6 space-y-3">
                <a href="<?php echo e(route('admin.profile.edit')); ?>"
                   class="w-full material-button material-button-sm material-button-secondary flex items-center justify-center">
                    <i class="material-icons mr-2 text-sm">edit</i>
                    Edit Profile
                </a>
            </div>
        </div>
    </div>

    <!-- User Information -->
    <div class="lg:col-span-2 space-y-6">
        <!-- Basic Information -->
        <div class="material-card">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="material-icons mr-2">person</i>
                    Basic Information
                </h3>
            </div>
            <div class="p-6">
                <dl class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Full Name</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($user->name); ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Email Address</dt>
                        <dd class="mt-1 text-sm text-gray-900"><?php echo e($user->email); ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Account Type</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <?php if($user->roles->isNotEmpty()): ?>
                                <span class="text-purple-600 font-medium"><?php echo e($user->roles->first()->name); ?></span>
                            <?php else: ?>
                                <span class="text-gray-600">Regular User</span>
                            <?php endif; ?>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Email Verification</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <?php if($user->email_verified_at): ?>
                                <span class="text-green-600">Verified on <?php echo e($user->email_verified_at->format('M d, Y')); ?></span>
                            <?php else: ?>
                                <span class="text-red-600">Not verified</span>
                            <?php endif; ?>
                        </dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Account Activity -->
        <div class="material-card">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="material-icons mr-2">schedule</i>
                    Account Activity
                </h3>
            </div>
            <div class="p-6">
                <dl class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Account Created</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <?php echo e($user->created_at->format('M d, Y \a\t g:i A')); ?>

                            <span class="text-gray-500">(<?php echo e($user->created_at->diffForHumans()); ?>)</span>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <?php echo e($user->updated_at->format('M d, Y \a\t g:i A')); ?>

                            <span class="text-gray-500">(<?php echo e($user->updated_at->diffForHumans()); ?>)</span>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">User ID</dt>
                        <dd class="mt-1 text-sm text-gray-900 font-mono">#<?php echo e($user->id); ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Account Status</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                Active
                            </span>
                        </dd>
                    </div>
                </dl>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\projects\client-admin-panel\resources\views/admin/profile/show.blade.php ENDPATH**/ ?>