@extends('layouts.admin')

@section('title', 'Create News')

@php
    $pageTitle = 'Create New News';
    $pageDescription = 'Add a new news article with categories and tags';
    $breadcrumbs = [
        ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
        ['title' => 'News', 'url' => route('admin.news.index')],
        ['title' => 'Create', 'url' => '#']
    ];
@endphp

@section('page-header')
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-semibold text-gray-900">{{ $pageTitle }}</h1>
            <p class="mt-2 text-gray-600">{{ $pageDescription }}</p>
        </div>
        <a href="{{ route('admin.news.index') }}" class="material-button material-button-secondary flex items-center">
            <i class="material-icons mr-2">arrow_back</i>
            Back to News
        </a>
    </div>
@endsection

@section('content')

    <!-- Create News Form -->
    <div class="material-card">
        <form method="POST" action="{{ route('admin.news.store') }}" class="p-6" id="news_form" enctype="multipart/form-data">
            @csrf

            <!-- Form Layout - Responsive Grid -->
            <div class="space-y-6">
                <!-- Row 1: Title and Slug -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Title Field -->
                    <div class="order-1 lg:order-1">
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                            Title <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">article</i>
                            </div>
                            <input id="title" name="title" type="text" required value="{{ old('title') }}"
                                class="material-input-with-icon @error('title') material-input-error @enderror"
                                placeholder="Enter news title">
                        </div>
                        @error('title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Slug Field -->
                    <div class="order-2 lg:order-2">
                        <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">
                            Slug <small class="text-gray-500">(auto-generated)</small>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">link</i>
                            </div>
                            <input id="slug" name="slug" type="text" value="{{ old('slug') }}"
                                class="material-input-with-icon @error('slug') material-input-error @enderror"
                                placeholder="Auto-generated from title">
                        </div>
                        @error('slug')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Row 2: Description (Full Width) -->
                <div class="grid grid-cols-1 gap-6">
                    <div class="order-3">
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                            Description <span class="text-red-500">*</span>
                        </label>
                        <div class="ckeditor-container">
                            <textarea id="description" name="description" required
                                class="ckeditor @error('description') border-red-300 @enderror"
                                placeholder="Enter news description">{{ old('description') }}</textarea>
                        </div>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Row 4: Main Image (Full Width) -->
                <div class="grid grid-cols-1 gap-6">
                    <div class="order-6">
                        <label for="main_image" class="block text-sm font-medium text-gray-700 mb-2">
                            Main Image <span class="text-red-500">*</span>
                        </label>
                        <div class="mt-1 flex items-center space-x-4">
                            <!-- Image Preview -->
                            <div class="flex-shrink-0">
                                <img id="main-image-preview"
                                    class="h-20 w-32 rounded-lg object-cover border-2 border-gray-300 bg-gray-100"
                                    src="data:image/svg+xml,%3csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100' height='100' fill='%23f3f4f6'/%3e%3ctext x='50%25' y='50%25' font-size='14' text-anchor='middle' dy='.3em' fill='%236b7280'%3eNo Image%3c/text%3e%3c/svg%3e"
                                    alt="Main image preview">
                            </div>
                            <!-- File Input -->
                            <div class="flex-1">
                                <div class="relative">
                                    <input id="main_image" name="main_image" type="file" accept="image/*"
                                        class="hidden" onchange="previewMainImage(this)">
                                    <label for="main_image"
                                        class="material-button material-button-sm material-button-secondary cursor-pointer flex items-center">
                                        <i class="material-icons mr-2 text-sm">upload</i>
                                        Choose Main Image
                                    </label>
                                </div>
                                <p class="mt-1 text-xs text-gray-500">PNG, JPG, GIF up to 2MB. Required for news article.</p>
                            </div>
                        </div>
                        @error('main_image')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Row 5: Sub Image (Full Width) -->
                <div class="grid grid-cols-1 gap-6">
                    <div class="order-7">
                        <label for="sub_image" class="block text-sm font-medium text-gray-700 mb-2">
                            Sub Image <small class="text-gray-500">(optional)</small>
                        </label>
                        <div class="mt-1 flex items-center space-x-4">
                            <!-- Image Preview -->
                            <div class="flex-shrink-0">
                                <img id="sub-image-preview"
                                    class="h-20 w-32 rounded-lg object-cover border-2 border-gray-300 bg-gray-100"
                                    src="data:image/svg+xml,%3csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100' height='100' fill='%23f3f4f6'/%3e%3ctext x='50%25' y='50%25' font-size='14' text-anchor='middle' dy='.3em' fill='%236b7280'%3eNo Image%3c/text%3e%3c/svg%3e"
                                    alt="Sub image preview">
                            </div>
                            <!-- File Input -->
                            <div class="flex-1">
                                <div class="relative">
                                    <input id="sub_image" name="sub_image" type="file" accept="image/*"
                                        class="hidden" onchange="previewSubImage(this)">
                                    <label for="sub_image"
                                        class="material-button material-button-sm material-button-secondary cursor-pointer flex items-center">
                                        <i class="material-icons mr-2 text-sm">upload</i>
                                        Choose Sub Image
                                    </label>
                                </div>
                                <p class="mt-1 text-xs text-gray-500">PNG, JPG, GIF up to 2MB. Optional secondary image.</p>
                            </div>
                        </div>
                        @error('sub_image')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Row 3: Categories and Tags -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Categories Field -->
                    <div class="order-4 lg:order-1">
                        <label for="categories" class="block text-sm font-medium text-gray-700 mb-2">
                            Categories <span class="text-red-500">*</span>
                        </label>
                        <div class="mb-2">
                            <select id="categories" name="categories[]" class="select2-multiple w-full" multiple>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" {{ in_array($category->id, old('categories', [])) ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        @error('categories')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Tags Field -->
                    <div class="order-5 lg:order-2">
                        <label for="tags" class="block text-sm font-medium text-gray-700 mb-2">
                            Tags <small class="text-gray-500">(optional)</small>
                        </label>
                        <div class="mb-2">
                            <select id="tags" name="tags[]" class="select2-multiple w-full" multiple>
                                @foreach($tags as $tag)
                                    <option value="{{ $tag->id }}" {{ in_array($tag->id, old('tags', [])) ? 'selected' : '' }}>
                                        {{ $tag->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        @error('tags')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Status and Settings Row -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Status -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status *</label>
                        <select name="status" id="status" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('status') border-red-500 @enderror">
                            <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                        @error('status')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Sort Order -->
                    <div>
                        <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
                        <input type="number" name="sort_order" id="sort_order" value="{{ old('sort_order', 0) }}" min="0" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('sort_order') border-red-500 @enderror">
                        @error('sort_order')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Featured -->
                    <div class="flex items-center pt-6">
                        <input type="hidden" name="is_featured" value="0">
                        <input type="checkbox" name="is_featured" id="is_featured" value="1" {{ old('is_featured') ? 'checked' : '' }}
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="is_featured" class="ml-2 block text-sm text-gray-900">Featured News</label>
                    </div>
                </div>

                <!-- Published At -->
                <div>
                    <label for="published_at" class="block text-sm font-medium text-gray-700 mb-2">Published At</label>
                    <input type="datetime-local" name="published_at" id="published_at" value="{{ old('published_at') }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('published_at') border-red-500 @enderror">
                    <p class="mt-1 text-xs text-gray-500">Leave empty to auto-set when status is active</p>
                    @error('published_at')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- SEO Meta Section -->
            <div class="space-y-6 mt-8">
                <div class="border-b border-gray-200 pb-4">
                    <h3 class="text-lg font-medium text-gray-900">SEO Meta Information</h3>
                    <p class="mt-1 text-sm text-gray-600">Optimize your news for search engines</p>
                </div>

                <!-- Meta Title and Meta Description Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-2">Meta Title</label>
                        <input type="text" name="meta_title" id="meta_title" value="{{ old('meta_title') }}" maxlength="255"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('meta_title') border-red-500 @enderror">
                        @error('meta_title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">Meta Description</label>
                        <textarea name="meta_description" id="meta_description" rows="3" maxlength="500"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('meta_description') border-red-500 @enderror">{{ old('meta_description') }}</textarea>
                        @error('meta_description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Meta Keywords and Canonical URL Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="meta_keywords" class="block text-sm font-medium text-gray-700 mb-2">Meta Keywords</label>
                        <textarea name="meta_keywords" id="meta_keywords" rows="2"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('meta_keywords') border-red-500 @enderror"
                                  placeholder="keyword1, keyword2, keyword3">{{ old('meta_keywords') }}</textarea>
                        @error('meta_keywords')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="canonical_url" class="block text-sm font-medium text-gray-700 mb-2">Canonical URL</label>
                        <input type="url" name="canonical_url" id="canonical_url" value="{{ old('canonical_url') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('canonical_url') border-red-500 @enderror">
                        @error('canonical_url')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Robots Meta -->
                <div>
                    <label for="robots_meta" class="block text-sm font-medium text-gray-700 mb-2">Robots Meta</label>
                    <select name="robots_meta" id="robots_meta"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('robots_meta') border-red-500 @enderror">
                        <option value="index,follow" {{ old('robots_meta') == 'index,follow' ? 'selected' : '' }}>Index, Follow</option>
                        <option value="noindex,follow" {{ old('robots_meta') == 'noindex,follow' ? 'selected' : '' }}>No Index, Follow</option>
                        <option value="index,nofollow" {{ old('robots_meta') == 'index,nofollow' ? 'selected' : '' }}>Index, No Follow</option>
                        <option value="noindex,nofollow" {{ old('robots_meta') == 'noindex,nofollow' ? 'selected' : '' }}>No Index, No Follow</option>
                    </select>
                    @error('robots_meta')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Open Graph Section -->
            <div class="space-y-6 mt-8">
                <div class="border-b border-gray-200 pb-4">
                    <h3 class="text-lg font-medium text-gray-900">Open Graph Information</h3>
                    <p class="mt-1 text-sm text-gray-600">Control how your news appears when shared on social media</p>
                </div>

                <!-- OG Title and OG Description Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="og_title" class="block text-sm font-medium text-gray-700 mb-2">OG Title</label>
                        <input type="text" name="og_title" id="og_title" value="{{ old('og_title') }}" maxlength="255"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('og_title') border-red-500 @enderror">
                        @error('og_title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="og_description" class="block text-sm font-medium text-gray-700 mb-2">OG Description</label>
                        <textarea name="og_description" id="og_description" rows="3" maxlength="500"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('og_description') border-red-500 @enderror">{{ old('og_description') }}</textarea>
                        @error('og_description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- OG Image (Full Width) -->
                <div class="grid grid-cols-1 gap-6">
                    <div>
                        <label for="og_image" class="block text-sm font-medium text-gray-700 mb-2">
                            OG Image <small class="text-gray-500">(recommended: 1200x630px)</small>
                        </label>
                        <div class="mt-1 flex items-center space-x-4">
                            <!-- Image Preview -->
                            <div class="flex-shrink-0">
                                <img id="og-image-preview"
                                    class="h-20 w-32 rounded-lg object-cover border-2 border-gray-300 bg-gray-100"
                                    src="data:image/svg+xml,%3csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100' height='100' fill='%23f3f4f6'/%3e%3ctext x='50%25' y='50%25' font-size='14' text-anchor='middle' dy='.3em' fill='%236b7280'%3eNo Image%3c/text%3e%3c/svg%3e"
                                    alt="OG image preview">
                            </div>
                            <!-- File Input -->
                            <div class="flex-1">
                                <div class="relative">
                                    <input id="og_image" name="og_image" type="file" accept="image/*"
                                        class="hidden" onchange="previewOgImage(this)">
                                    <label for="og_image"
                                        class="material-button material-button-sm material-button-secondary cursor-pointer flex items-center">
                                        <i class="material-icons mr-2 text-sm">upload</i>
                                        Choose OG Image
                                    </label>
                                </div>
                                <p class="mt-1 text-xs text-gray-500">PNG, JPG, GIF up to 2MB. Recommended: 1200x630px for social sharing.</p>
                            </div>
                        </div>
                        @error('og_image')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- OG Type -->
                <div class="grid grid-cols-1 gap-6">
                    <div>
                        <label for="og_type" class="block text-sm font-medium text-gray-700 mb-2">OG Type</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">category</i>
                            </div>
                            <select id="og_type" name="og_type"
                                class="material-select-with-icon @error('og_type') material-select-error @enderror">
                                <option value="article" {{ old('og_type') == 'article' ? 'selected' : '' }}>Article</option>
                                <option value="website" {{ old('og_type') == 'website' ? 'selected' : '' }}>Website</option>
                                <option value="blog" {{ old('og_type') == 'blog' ? 'selected' : '' }}>Blog</option>
                            </select>
                        </div>
                        @error('og_type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 mt-8">
                <a href="{{ route('admin.news.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-400 focus:bg-gray-400 active:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    Cancel
                </a>
                <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <i class="material-icons mr-2" style="font-size: 16px;">save</i>
                    Create News
                </button>
            </div>
        </form>
    </div>
@endsection

@push('styles')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<!-- CKEditor 5 Styles -->
<style>
    .ck-editor__editable {
        min-height: 300px;
    }
    .ck.ck-editor {
        max-width: 100%;
    }
    .ck-content {
        font-family: inherit;
    }
</style>
@endpush

@push('scripts')
    <!-- jQuery, Validation, and Select2 -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- CKEditor 5 -->
    <script src="https://cdn.ckeditor.com/ckeditor5/40.2.0/classic/ckeditor.js"></script>

    <script>
        $(document).ready(function() {
            // Initialize Select2
            $('#categories').select2({
                placeholder: 'Select categories',
                allowClear: true,
                width: '100%'
            });

            $('#tags').select2({
                placeholder: 'Select tags',
                allowClear: true,
                width: '100%'
            });

            // Initialize CKEditor
            ClassicEditor
                .create(document.querySelector('#description'), {
                    toolbar: {
                        items: [
                            'heading', '|',
                            'bold', 'italic', 'link', '|',
                            'bulletedList', 'numberedList', '|',
                            'outdent', 'indent', '|',
                            'blockQuote', 'insertTable', '|',
                            'undo', 'redo'
                        ]
                    },
                    language: 'en',
                    table: {
                        contentToolbar: [
                            'tableColumn',
                            'tableRow',
                            'mergeTableCells'
                        ]
                    }
                })
                .then(editor => {
                    window.descriptionEditor = editor;

                    // Update validation when CKEditor content changes
                    editor.model.document.on('change:data', () => {
                        const data = editor.getData();
                        $('#description').val(data).trigger('change');
                    });
                })
                .catch(error => {
                    console.error('CKEditor initialization error:', error);
                });

            // Add custom validation methods
            $.validator.addMethod('extension', function(value, element, param) {
                if (this.optional(element)) {
                    return true;
                }
                var extensions = param.split('|');
                var fileName = value.toLowerCase();
                for (var i = 0; i < extensions.length; i++) {
                    if (fileName.endsWith('.' + extensions[i])) {
                        return true;
                    }
                }
                return false;
            }, 'Please select a valid file type.');

            // Custom file size validation method
            $.validator.addMethod('filesize', function(value, element, param) {
                return this.optional(element) || (element.files[0] && element.files[0].size <= param);
            }, 'File size must be less than 2MB');

            // Custom CKEditor validation method
            $.validator.addMethod('ckeditor', function(value, element) {
                if (window.descriptionEditor) {
                    const data = window.descriptionEditor.getData();
                    return data.trim().length > 0;
                }
                return value.trim().length > 0;
            }, 'Please enter a description');

            // Auto-generate slug from title
            $('#title').on('input', function() {
                const title = $(this).val();
                const slug = title.toLowerCase()
                    .replace(/[^a-z0-9\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .replace(/-+/g, '-')
                    .trim('-');
                $('#slug').val(slug);
            });

            // jQuery Form validation
            $("#news_form").validate({
                rules: {
                    title: {
                        required: true,
                        minlength: 2,
                        maxlength: 255
                    },
                    slug: {
                        maxlength: 255
                    },
                    description: {
                        required: function() {
                            // Check CKEditor content
                            if (window.descriptionEditor) {
                                const data = window.descriptionEditor.getData();
                                return data.trim() === '';
                            }
                            return $('#description').val().trim() === '';
                        },
                        minlength: 10
                    },
                    main_image: {
                        required: true,
                        extension: "jpg|jpeg|png|gif",
                        filesize: 2097152 // 2MB in bytes
                    },
                    sub_image: {
                        extension: "jpg|jpeg|png|gif",
                        filesize: 2097152 // 2MB in bytes
                    },
                    status: {
                        required: true
                    },
                    'categories[]': {
                        required: true,
                        minlength: 1
                    },
                    sort_order: {
                        number: true,
                        min: 0
                    },
                    meta_title: {
                        maxlength: 255
                    },
                    meta_description: {
                        maxlength: 500
                    },
                    canonical_url: {
                        url: true
                    },
                    og_title: {
                        maxlength: 255
                    },
                    og_description: {
                        maxlength: 500
                    },
                    og_image: {
                        extension: "jpg|jpeg|png|gif",
                        filesize: 2097152 // 2MB in bytes
                    }
                },
                messages: {
                    title: {
                        required: "Please enter a title",
                        minlength: "Title must be at least 2 characters long",
                        maxlength: "Title cannot exceed 255 characters"
                    },
                    description: {
                        required: "Please enter a description",
                        minlength: "Description must be at least 10 characters long"
                    },
                    main_image: {
                        required: "Please select a main image",
                        extension: "Please select a valid image file (JPG, JPEG, PNG, GIF)",
                        filesize: "File size must be less than 2MB"
                    },
                    'categories[]': {
                        required: "Please select at least one category",
                        minlength: "Please select at least one category"
                    }
                },
                errorElement: 'span',
                errorPlacement: function (error, element) {
                    if (element.attr("name") == "categories[]" || element.attr("name") == "tags[]") {
                        error.addClass("text-red-500 text-xs mt-1").insertAfter(element.next('.select2-container'));
                    } else {
                        error.addClass("text-red-500 text-xs mt-1").insertAfter(element.closest('.relative'));
                    }
                },
                highlight: function (element) {
                    $(element).addClass('border-red-500').removeClass('border-gray-300');
                    if ($(element).attr('name') == 'categories[]' || $(element).attr('name') == 'tags[]') {
                        $(element).next('.select2-container').find('.select2-selection').addClass('border-red-500').removeClass('border-gray-300');
                    }
                },
                unhighlight: function (element) {
                    $(element).removeClass('border-red-500').addClass('border-gray-300');
                    if ($(element).attr('name') == 'categories[]' || $(element).attr('name') == 'tags[]') {
                        $(element).next('.select2-container').find('.select2-selection').removeClass('border-red-500').addClass('border-gray-300');
                    }
                },
                submitHandler: function(form) {
                    // Ensure CKEditor data is synced before submission
                    if (window.descriptionEditor) {
                        const data = window.descriptionEditor.getData();
                        $('#description').val(data);
                    }
                    form.submit();
                }
            });
        });

        // Preview Main Image
        function previewMainImage(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('main-image-preview').src = e.target.result;
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // Preview Sub Image
        function previewSubImage(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('sub-image-preview').src = e.target.result;
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // Preview OG Image
        function previewOgImage(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('og-image-preview').src = e.target.result;
                };
                reader.readAsDataURL(input.files[0]);
            }
        }
    </script>
@endpush
