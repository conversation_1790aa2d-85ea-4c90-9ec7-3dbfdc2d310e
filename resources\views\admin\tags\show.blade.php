@extends('layouts.admin')

@section('title', 'View Tag')

@php
    $pageTitle = 'Tag Details';
    $pageDescription = 'View tag information and details';
    $breadcrumbs = [
        ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
        ['title' => 'Tags', 'url' => route('admin.tags.index')],
        ['title' => $tag->name, 'url' => '#']
    ];
@endphp

@section('page-header')
<div class="flex items-center justify-between">
    <div>
        <h1 class="text-3xl font-semibold text-gray-900">{{ $pageTitle }}</h1>
        <p class="mt-2 text-gray-600">{{ $pageDescription }}</p>
    </div>
    <div class="flex space-x-3">
        <a href="{{ route('admin.tags.edit', $tag) }}" class="material-button material-button-primary flex items-center">
            <i class="material-icons mr-2">edit</i>
            Edit Tag
        </a>
        <a href="{{ route('admin.tags.index') }}" class="material-button material-button-secondary flex items-center">
            <i class="material-icons mr-2">arrow_back</i>
            Back to Tags
        </a>
    </div>
</div>
@endsection

@section('content')

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Tag Profile Card -->
    <div class="lg:col-span-1">
        <div class="material-card p-6">
            <div class="text-center">
                <div class="w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-4 border-4 border-gray-100" style="background-color: {{ $tag->color_with_opacity }};">
                    <span class="font-bold text-2xl" style="color: {{ $tag->color }};">{{ substr($tag->name, 0, 1) }}</span>
                </div>
                <h3 class="text-xl font-semibold text-gray-900">{{ $tag->name }}</h3>
                <p class="text-gray-600 font-mono text-sm">{{ $tag->slug }}</p>

                <!-- Status Badges -->
                <div class="flex justify-center space-x-2 mt-4">
                    <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full {{ $tag->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                        <i class="material-icons mr-1 text-xs">{{ $tag->status === 'active' ? 'check_circle' : 'cancel' }}</i>
                        {{ ucfirst($tag->status) }}
                    </span>

                    <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                        <i class="material-icons mr-1 text-xs">local_offer</i>
                        Tag
                    </span>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="mt-6 space-y-3">
                <a href="{{ route('admin.tags.edit', $tag) }}"
                   class="w-full material-button material-button-sm material-button-secondary flex items-center justify-center">
                    <i class="material-icons mr-2 text-sm">edit</i>
                    Edit Tag
                </a>

                <button onclick="deleteTag()"
                        class="w-full material-button material-button-sm material-button-danger flex items-center justify-center">
                    <i class="material-icons mr-2 text-sm">delete</i>
                    Delete Tag
                </button>
            </div>
        </div>
    </div>

    <!-- Tag Information -->
    <div class="lg:col-span-2 space-y-6">
        <!-- Basic Information -->
        <div class="material-card">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Basic Information</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Tag Name</label>
                        <p class="text-gray-900 font-medium">{{ $tag->name }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Slug</label>
                        <p class="text-gray-900 font-mono text-sm bg-gray-50 px-2 py-1 rounded">{{ $tag->slug }}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Color</label>
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 rounded-full border-2 border-gray-200" style="background-color: {{ $tag->color_with_opacity }}; border-color: {{ $tag->color }};"></div>
                            <span class="text-gray-900 font-mono text-sm">{{ $tag->color }}</span>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Sort Order</label>
                        <p class="text-gray-900">{{ $tag->sort_order }}</p>
                    </div>
                </div>

                @if($tag->description)
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-900">{{ $tag->description }}</p>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Tag Statistics -->
        <div class="material-card">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Tag Statistics</h3>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600">{{ $tag->id }}</div>
                        <div class="text-sm text-gray-600">Tag ID</div>
                    </div>

                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600">{{ $tag->created_at->diffForHumans() }}</div>
                        <div class="text-sm text-gray-600">Created</div>
                    </div>

                    <div class="text-center">
                        <div class="text-3xl font-bold text-purple-600">{{ $tag->updated_at->diffForHumans() }}</div>
                        <div class="text-sm text-gray-600">Last Updated</div>
                    </div>
                </div>
            </div>
        </div>

            <!-- SEO Information -->
            @if($tag->meta_title || $tag->meta_description || $tag->meta_keywords || $tag->canonical_url)
            <div class="material-card">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">SEO Information</h3>
                    
                    <div class="space-y-4">
                        @if($tag->meta_title)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Meta Title</label>
                            <p class="text-gray-900">{{ $tag->meta_title }}</p>
                        </div>
                        @endif
                        
                        @if($tag->meta_description)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Meta Description</label>
                            <p class="text-gray-900">{{ $tag->meta_description }}</p>
                        </div>
                        @endif
                        
                        @if($tag->meta_keywords)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Meta Keywords</label>
                            <p class="text-gray-900">{{ $tag->meta_keywords }}</p>
                        </div>
                        @endif
                        
                        @if($tag->canonical_url)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Canonical URL</label>
                            <p class="text-gray-900">
                                <a href="{{ $tag->canonical_url }}" target="_blank" class="text-blue-600 hover:text-blue-800">
                                    {{ $tag->canonical_url }}
                                </a>
                            </p>
                        </div>
                        @endif
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Robots Meta</label>
                            <p class="text-gray-900">{{ $tag->robots_meta }}</p>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Open Graph Information -->
            @if($tag->og_title || $tag->og_description || $tag->og_image)
            <div class="material-card">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">Open Graph Information</h3>
                    
                    <div class="space-y-4">
                        @if($tag->og_title)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">OG Title</label>
                            <p class="text-gray-900">{{ $tag->og_title }}</p>
                        </div>
                        @endif
                        
                        @if($tag->og_description)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">OG Description</label>
                            <p class="text-gray-900">{{ $tag->og_description }}</p>
                        </div>
                        @endif
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">OG Type</label>
                            <p class="text-gray-900">{{ $tag->og_type }}</p>
                        </div>
                        
                        @if($tag->og_image)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">OG Image</label>
                            <div class="mt-2">
                                <img src="{{ $tag->og_image_url }}" 
                                     alt="OG Image" 
                                     class="w-32 h-32 object-cover rounded-lg border border-gray-200">
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
function deleteTag() {
    Swal.fire({
        title: 'Delete Tag',
        text: `Are you sure you want to delete "{{ $tag->name }}"? This action cannot be undone.`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc2626',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '{{ route("admin.tags.destroy", $tag) }}',
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            title: 'Deleted!',
                            text: response.message,
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        }).then(() => {
                            window.location.href = '{{ route("admin.tags.index") }}';
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Something went wrong while deleting the tag.',
                        icon: 'error'
                    });
                }
            });
        }
    });
}
</script>
@endpush
