/**
 * Common Utilities for Admin Panel
 *
 * Contains reusable JavaScript functions for the admin interface
 */

/**
 * Password Toggle Utility
 *
 * Initialize a single password toggle
 * @param {string} toggleId - ID of the toggle button
 * @param {string} inputId - ID of the password input
 * @param {string} iconId - ID of the icon element
 */
function initPasswordToggle(toggleId, inputId, iconId) {
    const toggleBtn = document.getElementById(toggleId);
    const passwordInput = document.getElementById(inputId);
    const passwordIcon = document.getElementById(iconId);

    if (toggleBtn && passwordInput && passwordIcon) {
        toggleBtn.addEventListener('click', function() {
            const isPassword = passwordInput.type === 'password';
            passwordInput.type = isPassword ? 'text' : 'password';
            passwordIcon.textContent = isPassword ? 'visibility_off' : 'visibility';
        });
    }
}

/**
 * Initialize all password toggles on the page
 * Automatically detects toggle buttons with data-password-toggle attribute
 * Format: data-password-toggle="inputId:iconId"
 */
function initPasswordToggles() {
    const toggleButtons = document.querySelectorAll('[data-password-toggle]');

    toggleButtons.forEach(button => {
        const config = button.getAttribute('data-password-toggle').split(':');
        if (config.length === 2) {
            const inputId = config[0];
            const iconId = config[1];

            const passwordInput = document.getElementById(inputId);
            const passwordIcon = document.getElementById(iconId);

            if (passwordInput && passwordIcon) {
                button.addEventListener('click', function() {
                    const isPassword = passwordInput.type === 'password';
                    passwordInput.type = isPassword ? 'text' : 'password';
                    passwordIcon.textContent = isPassword ? 'visibility_off' : 'visibility';
                });
            }
        }
    });
}

/**
 * Image Preview Utility
 *
 * Preview selected image file before upload
 * @param {HTMLInputElement} input - File input element
 * @param {string} previewId - ID of the preview image element (optional)
 * @param {number} maxSize - Maximum file size in bytes (default: 2MB)
 */
function previewImage(input, previewId = 'image-preview', maxSize = 2 * 1024 * 1024) {
    const preview = document.getElementById(previewId);
    const file = input.files[0];

    if (file) {
        // Validate file size
        if (file.size > maxSize) {
            alert('File size must be less than ' + (maxSize / (1024 * 1024)) + 'MB');
            input.value = '';
            return;
        }

        // Validate file type
        if (!file.type.startsWith('image/')) {
            alert('Please select a valid image file');
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            if (preview) {
                preview.src = e.target.result;
            }
        };
        reader.readAsDataURL(file);
    } else if (preview) {
        // Reset to default placeholder
        preview.src = "data:image/svg+xml,%3csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100' height='100' fill='%23f3f4f6'/%3e%3ctext x='50%25' y='50%25' font-size='14' text-anchor='middle' dy='.3em' fill='%236b7280'%3eNo Image%3c/text%3e%3c/svg%3e";
    }
}

/**
 * Form Validation Utility
 *
 * Validate password confirmation
 * @param {string} passwordId - ID of the password input
 * @param {string} confirmId - ID of the password confirmation input
 * @returns {boolean} - True if passwords match
 */
function validatePasswordConfirmation(passwordId, confirmId) {
    const password = document.getElementById(passwordId);
    const confirm = document.getElementById(confirmId);

    if (password && confirm) {
        return password.value === confirm.value;
    }

    return true;
}

/**
 * Remove Image Utility
 *
 * Remove profile image in edit forms
 * @param {Event} event - The click event (optional)
 */
function removeImage(event = null) {
    // Set the hidden input to indicate image removal
    const removeInput = document.getElementById('remove_image');
    if (removeInput) {
        removeInput.value = '1';
    }

    // Update the preview to show default placeholder
    const preview = document.getElementById('image-preview');
    if (preview) {
        preview.src = "data:image/svg+xml,%3csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100' height='100' fill='%23f3f4f6'/%3e%3ctext x='50%25' y='50%25' font-size='14' text-anchor='middle' dy='.3em' fill='%236b7280'%3eNo Image%3c/text%3e%3c/svg%3e";
    }

    // Hide the current image info and remove button
    const currentImageInfo = document.getElementById('current-image-info');
    const removeButton = event ? event.target.closest('button') : document.querySelector('button[onclick="removeImage()"]');

    if (currentImageInfo) currentImageInfo.style.display = 'none';
    if (removeButton) removeButton.style.display = 'none';

    // Update the upload button text
    const uploadLabel = document.querySelector('label[for="profile_image"]');
    if (uploadLabel) {
        uploadLabel.innerHTML = '<i class="material-icons mr-2 text-sm">upload</i>Upload Image';
    }

    // Clear the file input
    const fileInput = document.getElementById('profile_image');
    if (fileInput) {
        fileInput.value = '';
    }
}

// Make functions globally available
window.initPasswordToggle = initPasswordToggle;
window.initPasswordToggles = initPasswordToggles;
window.previewImage = previewImage;
window.validatePasswordConfirmation = validatePasswordConfirmation;
window.removeImage = removeImage;