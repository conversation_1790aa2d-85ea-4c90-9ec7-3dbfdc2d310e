<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Yajra\DataTables\Facades\DataTables;
use App\Helpers\DataTableHelper;

class CategoryController extends Controller
{
    /**
     * Display a listing of categories and handle datatable requests.
     */
    public function index(Request $request)
    {
        // Check if this is an AJAX request for datatable
        if ($request->ajax()) {
            $query = Category::with(['parent', 'children']);
            
            return DataTables::of($query)
                ->filter(function ($query) use ($request) {
                    if ($request->has('search') && $request->input('search.value')) {
                        $searchValue = $request->input('search.value');
                        $query->where(function ($q) use ($searchValue) {
                            $q->where('name', 'like', "%{$searchValue}%")
                                ->orWhere('slug', 'like', "%{$searchValue}%")
                                ->orWhere('description', 'like', "%{$searchValue}%")
                                ->orWhere('meta_title', 'like', "%{$searchValue}%");
                        });
                    }
                })
                ->addColumn('category', function ($category) {
                    $parentText = $category->parent ? '<div class="text-xs text-gray-500 mt-1">Parent: ' . $category->parent->name . '</div>' : '';
                    return '<div class="flex items-center">
                                <div class="w-4 h-4 rounded-full mr-3 border border-gray-200" style="background-color: ' . $category->color_with_opacity . ';"></div>
                                <div>
                                    <div class="text-gray-900 font-medium">' . $category->name . '</div>
                                    <div class="text-xs text-gray-500">' . $category->slug . '</div>
                                    ' . $parentText . '
                                </div>
                            </div>';
                })
                ->addColumn('status', function ($category) {
                    $isActive = $category->status === 'active';
                    return DataTableHelper::statusBadge(
                        $isActive, 
                        'Active', 
                        'Inactive', 
                        'verified', 
                        'unverified'
                    );
                })
                ->addColumn('featured', function ($category) {
                    return DataTableHelper::statusBadge(
                        $category->is_featured, 
                        'Featured', 
                        'Regular', 
                        'admin', 
                        'default'
                    );
                })
                ->addColumn('children_count', function ($category) {
                    $count = $category->children->count();
                    return '<div class="text-center">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    ' . $count . ' ' . Str::plural('child', $count) . '
                                </span>
                            </div>';
                })
                ->addColumn('actions', function ($category) {
                    return DataTableHelper::actionsColumn($category, 'admin.categories', [
                        'viewTitle' => 'View Category',
                        'editTitle' => 'Edit Category',
                        'deleteTitle' => 'Delete Category'
                    ]);
                })
                ->editColumn('created_at', function ($category) {
                    return $category->created_at->format('M d, Y');
                })
                ->rawColumns(['category', 'status', 'featured', 'children_count', 'actions'])
                ->make(true);
        }
        
        return view('admin.categories.index');
    }

    /**
     * Show the form for creating a new category.
     */
    public function create()
    {
        $parentCategories = Category::whereNull('parent_id')->active()->orderBy('name')->get();
        return view('admin.categories.create', compact('parentCategories'));
    }

    /**
     * Store a newly created category in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'slug' => ['nullable', 'string', 'max:255', 'unique:categories,slug'],
            'description' => ['nullable', 'string'],
            'color' => ['required', 'string', 'regex:/^#[0-9A-Fa-f]{6}$/'],
            'status' => ['required', 'in:active,inactive'],
            'parent_id' => ['nullable', 'exists:categories,id'],
            'sort_order' => ['nullable', 'integer', 'min:0'],
            'is_featured' => ['boolean'],
            'meta_title' => ['nullable', 'string', 'max:255'],
            'meta_description' => ['nullable', 'string', 'max:500'],
            'meta_keywords' => ['nullable', 'string'],
            'canonical_url' => ['nullable', 'url'],
            'robots_meta' => ['nullable', 'string'],
            'og_title' => ['nullable', 'string', 'max:255'],
            'og_description' => ['nullable', 'string', 'max:500'],
            'og_image' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
            'og_type' => ['nullable', 'string'],
        ]);

        $categoryData = $request->except(['og_image']);
        
        // Generate slug if not provided
        if (empty($categoryData['slug'])) {
            $categoryData['slug'] = Str::slug($categoryData['name']);
        }

        // Handle OG image upload
        if ($request->hasFile('og_image')) {
            $ogImagePath = $request->file('og_image')->store('categories/og-images', 'public');
            $categoryData['og_image'] = $ogImagePath;
        }

        Category::create($categoryData);
        
        return redirect()->route('admin.categories.index')->with('success', 'Category created successfully.');
    }

    /**
     * Display the specified category.
     */
    public function show(Category $category)
    {
        $category->load(['parent', 'children']);
        return view('admin.categories.show', compact('category'));
    }

    /**
     * Show the form for editing the specified category.
     */
    public function edit(Category $category)
    {
        $parentCategories = Category::whereNull('parent_id')
            ->where('id', '!=', $category->id)
            ->active()
            ->orderBy('name')
            ->get();
        
        return view('admin.categories.edit', compact('category', 'parentCategories'));
    }

    /**
     * Update the specified category in storage.
     */
    public function update(Request $request, Category $category)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'slug' => ['nullable', 'string', 'max:255', 'unique:categories,slug,' . $category->id],
            'description' => ['nullable', 'string'],
            'color' => ['required', 'string', 'regex:/^#[0-9A-Fa-f]{6}$/'],
            'status' => ['required', 'in:active,inactive'],
            'parent_id' => ['nullable', 'exists:categories,id'],
            'sort_order' => ['nullable', 'integer', 'min:0'],
            'is_featured' => ['boolean'],
            'meta_title' => ['nullable', 'string', 'max:255'],
            'meta_description' => ['nullable', 'string', 'max:500'],
            'meta_keywords' => ['nullable', 'string'],
            'canonical_url' => ['nullable', 'url'],
            'robots_meta' => ['nullable', 'string'],
            'og_title' => ['nullable', 'string', 'max:255'],
            'og_description' => ['nullable', 'string', 'max:500'],
            'og_image' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
            'og_type' => ['nullable', 'string'],
            'remove_og_image' => ['boolean'],
        ]);

        $categoryData = $request->except(['og_image', 'remove_og_image']);
        
        // Generate slug if not provided
        if (empty($categoryData['slug'])) {
            $categoryData['slug'] = Str::slug($categoryData['name']);
        }

        // Handle OG image removal
        if ($request->boolean('remove_og_image') && $category->og_image) {
            Storage::disk('public')->delete($category->og_image);
            $categoryData['og_image'] = null;
        }

        // Handle OG image upload
        if ($request->hasFile('og_image')) {
            // Delete old image if exists
            if ($category->og_image) {
                Storage::disk('public')->delete($category->og_image);
            }
            
            $ogImagePath = $request->file('og_image')->store('categories/og-images', 'public');
            $categoryData['og_image'] = $ogImagePath;
        }

        $category->update($categoryData);
        
        return redirect()->route('admin.categories.index')->with('success', 'Category updated successfully.');
    }

    /**
     * Remove the specified category from storage.
     */
    public function destroy(Category $category)
    {
        // Check if category has children
        if ($category->children()->count() > 0) {
            return redirect()->route('admin.categories.index')
                ->with('error', 'Cannot delete category with child categories. Please delete or reassign child categories first.');
        }

        // Delete OG image if exists
        if ($category->og_image) {
            Storage::disk('public')->delete($category->og_image);
        }

        $category->delete();
        
        return redirect()->route('admin.categories.index')->with('success', 'Category deleted successfully.');
    }
}
