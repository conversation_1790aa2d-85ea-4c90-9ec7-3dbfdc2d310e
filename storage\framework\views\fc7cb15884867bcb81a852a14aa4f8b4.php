<?php $__env->startSection('title', 'Edit Profile'); ?>

<?php
    $pageTitle = 'Edit Profile';
    $pageDescription = 'Update your profile information';
    $breadcrumbs = [
        ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
        ['title' => 'My Profile', 'url' => route('admin.profile.show')],
        ['title' => 'Edit', 'url' => '#']
    ];
?>

<?php $__env->startSection('page-header'); ?>
<div class="flex items-center justify-between">
    <div>
        <h1 class="text-3xl font-semibold text-gray-900"><?php echo e($pageTitle); ?></h1>
        <p class="mt-2 text-gray-600"><?php echo e($pageDescription); ?></p>
    </div>
    <div class="flex space-x-3">
        <a href="<?php echo e(route('admin.profile.show')); ?>" class="material-button material-button-md material-button-secondary flex items-center">
            <i class="material-icons mr-2">arrow_back</i>
            Back to Profile
        </a>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<form id="profile_edit_form" method="POST" action="<?php echo e(route('admin.profile.update')); ?>" enctype="multipart/form-data" class="space-y-6">
    <?php echo csrf_field(); ?>
    <?php echo method_field('PUT'); ?>

    <div class="material-card">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Profile Information</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Full Name <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">person</i>
                        </div>
                        <input type="text"
                               id="name"
                               name="name"
                               value="<?php echo e(old('name', $user->name)); ?>"
                               class="material-input-with-icon <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-input-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               placeholder="Enter full name"
                               required>
                    </div>
                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Email -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        Email Address <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">email</i>
                        </div>
                        <input type="email"
                               id="email"
                               name="email"
                               value="<?php echo e(old('email', $user->email)); ?>"
                               class="material-input-with-icon <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-input-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               placeholder="Enter email address"
                               required>
                    </div>
                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>

            <!-- Profile Image -->
            <div class="mt-6">
                <label for="profile_image" class="block text-sm font-medium text-gray-700 mb-2">
                    Profile Image <small class="text-gray-500">(optional)</small>
                </label>
                <div class="mt-1 flex items-center space-x-4">
                    <!-- Image Preview -->
                    <div class="flex-shrink-0">
                        <img id="image-preview" class="h-20 w-20 rounded-full object-cover border-2 border-gray-300 bg-gray-100"
                             src="<?php echo e($user->profile_image_url); ?>"
                             alt="Profile preview">
                    </div>
                    <!-- File Input and Actions -->
                    <div class="flex-1">
                        <div class="flex flex-wrap gap-2 mb-2">
                            <div class="relative">
                                <input id="profile_image"
                                       name="profile_image"
                                       type="file"
                                       accept="image/*"
                                       class="hidden"
                                       onchange="previewImage(this)">
                                <label for="profile_image" class="material-button material-button-sm material-button-secondary cursor-pointer flex items-center">
                                    <i class="material-icons mr-2 text-sm">upload</i>
                                    <?php echo e($user->profile_image ? 'Change Image' : 'Upload Image'); ?>

                                </label>
                            </div>
                            <?php if($user->profile_image): ?>
                                <button type="button" onclick="removeImage()" class="material-button material-button-sm material-button-danger flex items-center">
                                    <i class="material-icons mr-2 text-sm">delete</i>
                                    Remove Image
                                </button>
                                <!-- Hidden input to track image removal -->
                                <input type="hidden" id="remove_image" name="remove_image" value="0">
                            <?php endif; ?>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
                        <?php if($user->profile_image): ?>
                            <p class="mt-1 text-xs text-gray-600" id="current-image-info">Current: <?php echo e(basename($user->profile_image)); ?></p>
                        <?php endif; ?>
                        <?php $__errorArgs = ['profile_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Password Change Section -->
    <div class="material-card">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Change Password</h3>
            <p class="text-sm text-gray-600">Leave blank to keep your current password</p>
        </div>
        <div class="p-6">
            <div class="space-y-6">
                <!-- Current Password -->
                <div>
                    <label for="current_password" class="block text-sm font-medium text-gray-700 mb-2">
                        Current Password
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">lock</i>
                        </div>
                        <input id="current_password"
                            name="current_password"
                            type="password"
                            class="material-input-with-icon <?php $__errorArgs = ['current_password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-input-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            placeholder="Enter current password">
                        <button type="button"
                            id="toggle-current-password"
                            data-password-toggle="current_password:current-password-icon"
                            class="absolute inset-y-0 right-0 px-3 flex items-center z-10 focus:outline-none rounded">
                            <i class="material-icons text-gray-400 hover:text-gray-600 text-lg" id="current-password-icon">visibility</i>
                        </button>
                    </div>
                    <?php $__errorArgs = ['current_password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- New Password and Confirm Password -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- New Password -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                            New Password
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">lock</i>
                            </div>
                            <input id="password"
                                name="password"
                                type="password"
                                class="material-input-with-icon <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-input-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                placeholder="Enter new password">
                            <button type="button"
                                id="toggle-password"
                                data-password-toggle="password:password-icon"
                                class="absolute inset-y-0 right-0 px-3 flex items-center z-10 focus:outline-none rounded">
                                <i class="material-icons text-gray-400 hover:text-gray-600 text-lg" id="password-icon">visibility</i>
                            </button>
                        </div>
                        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Confirm Password -->
                    <div>
                        <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">
                            Confirm New Password
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">lock</i>
                            </div>
                            <input id="password_confirmation"
                                name="password_confirmation"
                                type="password"
                                class="material-input-with-icon"
                                placeholder="Confirm new password">
                            <button type="button"
                                id="toggle-password-confirm"
                                data-password-toggle="password_confirmation:password-confirm-icon"
                                class="absolute inset-y-0 right-0 px-3 flex items-center z-10 focus:outline-none rounded">
                                <i class="material-icons text-gray-400 hover:text-gray-600 text-lg" id="password-confirm-icon">visibility</i>
                            </button>
                        </div>
                        <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Form Actions -->
    <div class="flex items-center justify-end space-x-4">
        <a href="<?php echo e(route('admin.profile.show')); ?>" class="material-button material-button-md material-button-secondary flex items-center">
            <i class="material-icons mr-2">cancel</i>
            Cancel
        </a>
        <button type="submit" class="material-button material-button-md material-button-primary flex items-center">
            <i class="material-icons mr-2">save</i>
            Update Profile
        </button>
    </div>
</form>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<?php echo app('Illuminate\Foundation\Vite')('resources/js/utils/common.js'); ?>
<script src="https://code.jquery.com/jquery-3.7.0.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize password toggles
        initPasswordToggle('toggle-current-password', 'current_password', 'current-password-icon');
        initPasswordToggle('toggle-password', 'password', 'password-icon');
        initPasswordToggle('toggle-password-confirm', 'password_confirmation', 'password-confirm-icon');

        // Add custom validation method for current password when changing password
        $.validator.addMethod("currentPasswordRequired", function(value, element) {
            const newPassword = $('#password').val();
            return !newPassword || (newPassword && value);
        }, "Current password is required when changing password");

        // Add custom validation method for password confirmation
        $.validator.addMethod("passwordConfirmation", function(value, element) {
            const password = $('#password').val();
            return !password || value === password;
        }, "New passwords do not match");

        // Form validation
        $("#profile_edit_form").validate({
            rules: {
                name: {
                    required: true,
                    minlength: 2,
                    maxlength: 255
                },
                email: {
                    required: true,
                    email: true,
                    maxlength: 255
                },
                current_password: {
                    currentPasswordRequired: true,
                    minlength: 8
                },
                password: {
                    minlength: 8,
                    maxlength: 255
                },
                password_confirmation: {
                    passwordConfirmation: true
                },
                profile_image: {
                    extension: "jpg|jpeg|png|gif"
                }
            },
            messages: {
                name: {
                    required: "Please enter your full name",
                    minlength: "Name must be at least 2 characters",
                    maxlength: "Name cannot exceed 255 characters"
                },
                email: {
                    required: "Please enter your email address",
                    email: "Please enter a valid email address",
                    maxlength: "Email cannot exceed 255 characters"
                },
                current_password: {
                    minlength: "Current password must be at least 8 characters"
                },
                password: {
                    minlength: "New password must be at least 8 characters",
                    maxlength: "Password cannot exceed 255 characters"
                },
                profile_image: {
                    extension: "Please select a valid image file (JPG, JPEG, PNG, GIF)"
                }
            },
            errorElement: 'span',
            errorPlacement: function (error, element) {
                error.addClass("text-red-500 text-xs mt-1").insertAfter(element.closest('.relative').length ? element.closest('.relative') : element);
            },
            highlight: function (element) {
                $(element).addClass('material-input-error border-red-500').removeClass('border-gray-300');
            },
            unhighlight: function (element) {
                $(element).removeClass('material-input-error border-red-500').addClass('border-gray-300');
            },
            submitHandler: function(form) {
                // Show loading state
                const submitBtn = $(form).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.prop('disabled', true).html('<i class="material-icons mr-2">hourglass_empty</i>Updating...');

                // Submit the form
                form.submit();
            }
        });

        // Real-time validation for password confirmation
        $('#password, #password_confirmation').on('keyup', function() {
            $('#password_confirmation').valid();
        });

        // Real-time validation for current password when new password is entered
        $('#password').on('keyup', function() {
            $('#current_password').valid();
        });
    });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\projects\client-admin-panel\resources\views/admin/profile/edit.blade.php ENDPATH**/ ?>