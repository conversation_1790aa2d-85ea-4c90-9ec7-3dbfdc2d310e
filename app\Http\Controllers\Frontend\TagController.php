<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Tag;
use Illuminate\Http\Request;

class TagController extends Controller
{
    public function show($slug)
    {
        $tag = Tag::where('slug', $slug)->firstOrFail();
        $news = $tag->news()->published()->latest()->paginate(12);
        
        return view('frontend.tags.show', compact('tag', 'news'));
    }
}