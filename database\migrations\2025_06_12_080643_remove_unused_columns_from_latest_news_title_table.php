<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('latest_news_title', function (Blueprint $table) {
            $table->dropColumn([
                'category',
                'published_by',
                'source_url',
                'description'
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('latest_news_title', function (Blueprint $table) {
            $table->string('category')->after('title');
            $table->string('published_by')->after('category');
            $table->text('source_url')->nullable()->after('published_by');
            $table->text('description')->nullable()->after('source_url');
        });
    }
};
