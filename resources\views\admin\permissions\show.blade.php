@extends('layouts.admin')

@section('title', 'View Permission')

@php
    $pageTitle = 'Permission Details';
    $pageDescription = 'View permission information and assigned roles';
    $breadcrumbs = [
        ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
        ['title' => 'Permissions', 'url' => route('admin.permissions.index')],
        ['title' => 'View', 'url' => '#']
    ];
@endphp

@section('page-header')
<div class="flex items-center justify-between">
    <div>
        <h1 class="text-3xl font-semibold text-gray-900">{{ $pageTitle }}</h1>
        <p class="mt-2 text-gray-600">{{ $pageDescription }}</p>
    </div>
    <div class="flex space-x-3">
        <a href="{{ route('admin.permissions.edit', $permission) }}" class="material-button material-button-primary flex items-center">
            <i class="material-icons mr-2">edit</i>
            Edit Permission
        </a>
        <a href="{{ route('admin.permissions.index') }}" class="material-button material-button-secondary flex items-center">
            <i class="material-icons mr-2">arrow_back</i>
            Back to Permissions
        </a>
    </div>
</div>
@endsection

@section('content')

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Permission Profile Card -->
    <div class="lg:col-span-1">
        <div class="material-card p-6">
            <div class="text-center">
                <div class="w-24 h-24 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-white font-bold text-2xl">{{ substr($permission->name, 0, 1) }}</span>
                </div>
                <h3 class="text-xl font-semibold text-gray-900">{{ $permission->name }}</h3>
                <p class="text-gray-600">{{ $permission->slug }}</p>
                
                <!-- Status Badges -->
                <div class="flex justify-center space-x-2 mt-4">
                    <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                        <i class="material-icons mr-1 text-xs">group</i>
                        {{ $permission->roles->count() }} Roles
                    </span>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="mt-6 space-y-3">
                <a href="{{ route('admin.permissions.edit', $permission) }}"
                   class="w-full material-button material-button-sm material-button-secondary flex items-center justify-center">
                    <i class="material-icons mr-2">edit</i>
                    Edit Permission
                </a>

                <form method="POST" action="{{ route('admin.permissions.destroy', $permission) }}"
                      class="delete-form">
                    @csrf
                    @method('DELETE')
                    <button type="submit"
                            class="w-full material-button material-button-sm material-button-danger flex items-center justify-center">
                        <i class="material-icons mr-2">delete</i>
                        Delete Permission
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Permission Information -->
    <div class="lg:col-span-2 space-y-6">
        <!-- Basic Information -->
        <div class="material-card">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="material-icons mr-2">info</i>
                    Basic Information
                </h3>
            </div>
            <div class="p-6">
                <dl class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Permission Name</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $permission->name }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Slug</dt>
                        <dd class="mt-1 text-sm text-gray-900 font-mono">{{ $permission->slug }}</dd>
                    </div>
                    <div class="sm:col-span-2">
                        <dt class="text-sm font-medium text-gray-500">Description</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {{ $permission->description ?: 'No description provided' }}
                        </dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Permission Activity -->
        <div class="material-card">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="material-icons mr-2">schedule</i>
                    Permission Activity
                </h3>
            </div>
            <div class="p-6">
                <dl class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Created</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {{ $permission->created_at->format('M d, Y \a\t g:i A') }}
                            <span class="text-gray-500">({{ $permission->created_at->diffForHumans() }})</span>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {{ $permission->updated_at->format('M d, Y \a\t g:i A') }}
                            <span class="text-gray-500">({{ $permission->updated_at->diffForHumans() }})</span>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Permission ID</dt>
                        <dd class="mt-1 text-sm text-gray-900 font-mono">#{{ $permission->id }}</dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Roles with this Permission -->
        <div class="material-card">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="material-icons mr-2">verified_user</i>
                    Roles with this Permission ({{ $permission->roles->count() }})
                </h3>
            </div>
            <div class="p-6">
                @if($permission->roles->count() > 0)
                    <div class="space-y-4">
                        @foreach($permission->roles as $role)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                                        <span class="text-white font-bold text-sm">{{ substr($role->name, 0, 1) }}</span>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-900">{{ $role->name }}</div>
                                        <div class="text-xs text-gray-500">{{ $role->slug }}</div>
                                    </div>
                                </div>
                                <a href="{{ route('admin.roles.show', $role) }}" class="text-blue-600 hover:text-blue-800">
                                    <i class="material-icons">visibility</i>
                                </a>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4 text-gray-500">
                        <i class="material-icons text-4xl mb-2">no_accounts</i>
                        <p>No roles have been assigned this permission</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    // Handle delete confirmation with SweetAlert2
    $(document).on('submit', '.delete-form', function(e) {
        e.preventDefault();
        const form = this;
        
        Swal.fire({
            title: 'Are you sure?',
            text: "You are about to delete this permission. This action cannot be undone!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });
    });
</script>
@endpush
