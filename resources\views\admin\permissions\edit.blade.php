@extends('layouts.admin')

@section('title', 'Edit Permission')

@php
    $pageTitle = 'Edit Permission';
    $pageDescription = 'Update permission information';
    $breadcrumbs = [
        ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
        ['title' => 'Permissions', 'url' => route('admin.permissions.index')],
        ['title' => 'Edit', 'url' => '#']
    ];
@endphp

@section('page-header')
<div class="flex items-center justify-between">
    <div>
        <h1 class="text-3xl font-semibold text-gray-900">{{ $pageTitle }}</h1>
        <p class="mt-2 text-gray-600">{{ $pageDescription }} for {{ $permission->name }}</p>
    </div>
    <div class="flex space-x-3">
        <a href="{{ route('admin.permissions.show', $permission) }}" class="material-button material-button-md material-button-secondary flex items-center">
            <i class="material-icons mr-2">visibility</i>
            View Permission
        </a>
        <a href="{{ route('admin.permissions.index') }}" class="material-button material-button-md material-button-secondary flex items-center">
            <i class="material-icons mr-2">arrow_back</i>
            Back to Permissions
        </a>
    </div>
</div>
@endsection

@section('content')
<!-- Edit Permission Form -->
<div class="material-card">
    <form method="POST" action="{{ route('admin.permissions.update', $permission) }}" class="p-6 space-y-6" id="permission_edit_form">
        @csrf
        @method('PUT')

        <!-- Name and Slug Fields - Side by Side -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Name Field -->
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                    Permission Name <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                        <i class="material-icons text-gray-400 text-lg">verified_user</i>
                    </div>
                    <input id="name" 
                           name="name" 
                           type="text" 
                           required 
                           value="{{ old('name', $permission->name) }}"
                           class="material-input-with-icon @error('name') border-red-500 focus:ring-red-500 @enderror"
                           placeholder="Enter permission name">
                </div>
                @error('name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Slug Field -->
            <div>
                <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">
                    Slug <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                        <i class="material-icons text-gray-400 text-lg">key</i>
                    </div>
                    <input id="slug" 
                           name="slug" 
                           type="text" 
                           required 
                           value="{{ old('slug', $permission->slug) }}"
                           class="material-input-with-icon @error('slug') border-red-500 focus:ring-red-500 @enderror"
                           placeholder="Enter permission slug">
                </div>
                @error('slug')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
                <p class="mt-1 text-sm text-gray-500">Unique identifier (e.g. "dashboard.view").</p>
            </div>
        </div>

        <!-- Description Field -->
        <div>
            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                Description
            </label>
            <textarea id="description" 
                      name="description" 
                      rows="3"
                      class="material-input @error('description') border-red-500 focus:ring-red-500 @enderror"
                      placeholder="Enter permission description">{{ old('description', $permission->description) }}</textarea>
            @error('description')
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
            @enderror
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            <a href="{{ route('admin.permissions.index') }}" class="material-button material-button-sm material-button-secondary flex items-center">
                Cancel
            </a>
            <button type="submit" class="material-button material-button-sm material-button-primary flex items-center">
                <i class="material-icons mr-2 text-sm">save</i>
                Update Permission
            </button>
        </div>
    </form>
</div>
@endsection

@push('scripts')
<script src="https://code.jquery.com/jquery-3.7.0.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
<script>
    $(document).ready(function() {
        // Set initial auto-generated state
        $('#slug').data('auto-generated', true);
        
        // Auto-generate slug from name
        $('#name').on('input', function() {
            // Only update slug if it's empty or if it was previously auto-generated
            if ($('#slug').data('auto-generated')) {
                const slug = $(this).val()
                    .toLowerCase()
                    .replace(/[^a-z0-9\s-\.]/g, '')
                    .replace(/[\s-]+/g, '_')
                    .replace(/^[._\s]+|[._\s]+$/g, '');
                $('#slug').val(slug);
            }
        });
        
        // When user manually edits slug, mark it as no longer auto-generated
        $('#slug').on('input', function() {
            if ($(this).val() !== $('#name').val().toLowerCase()
                    .replace(/[^a-z0-9\s-\.]/g, '')
                    .replace(/[\s-]+/g, '_')
                    .replace(/^[._\s]+|[._\s]+$/g, '')) {
                $(this).data('auto-generated', false);
            }
        });
        
        // Add custom validation method for slug
        $.validator.addMethod("slugFormat", function(value, element) {
            return this.optional(element) || /^[a-z0-9\-\.\_]+$/.test(value);
        }, "Slug can only contain lowercase letters, numbers, hyphens, underscores and dots");
        
        // Form validation
        $("#permission_edit_form").validate({
            rules: {
                name: {
                    required: true,
                    minlength: 3,
                    maxlength: 255
                },
                slug: {
                    required: true,
                    minlength: 3,
                    maxlength: 255,
                    slugFormat: true
                }
            },
            messages: {
                name: {
                    required: "Please enter a permission name",
                    minlength: "Permission name must be at least 3 characters"
                },
                slug: {
                    required: "Please enter a permission slug",
                    minlength: "Permission slug must be at least 3 characters"
                }
            },
            errorElement: 'p',
            errorClass: 'text-sm text-red-600 mt-1',
            errorPlacement: function(error, element) {
                error.insertAfter(element.closest('.relative'));
            },
            highlight: function(element) {
                $(element).addClass('border-red-500 focus:ring-red-500');
            },
            unhighlight: function(element) {
                $(element).removeClass('border-red-500 focus:ring-red-500');
            },
            submitHandler: function(form) {
                form.submit();
            }
        });
    });
</script>
@endpush














