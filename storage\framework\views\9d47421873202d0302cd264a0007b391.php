<?php $__env->startSection('title', 'Create Role'); ?>

<?php
    $pageTitle = 'Create New Role';
    $pageDescription = 'Add a new role with specific permissions';
    $breadcrumbs = [
        ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
        ['title' => 'Roles', 'url' => route('admin.roles.index')],
        ['title' => 'Create', 'url' => '#']
    ];
?>

<?php $__env->startSection('page-header'); ?>
<div class="flex items-center justify-between">
    <div>
        <h1 class="text-3xl font-semibold text-gray-900"><?php echo e($pageTitle); ?></h1>
        <p class="mt-2 text-gray-600"><?php echo e($pageDescription); ?></p>
    </div>
    <a href="<?php echo e(route('admin.roles.index')); ?>" class="material-button material-button-md material-button-secondary flex items-center">
        <i class="material-icons mr-2">arrow_back</i>
        Back to Roles
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Create Role Form -->
<div class="material-card">
    <form method="POST" action="<?php echo e(route('admin.roles.store')); ?>" class="p-6 space-y-6" id="role_form">
        <?php echo csrf_field(); ?>

        <!-- Name Field -->
        <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                Role Name <span class="text-red-500">*</span>
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                    <i class="material-icons text-gray-400 text-lg">badge</i>
                </div>
                <input id="name" 
                       name="name" 
                       type="text" 
                       required 
                       value="<?php echo e(old('name')); ?>"
                       class="material-input-with-icon <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-input-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       placeholder="Enter role name">
            </div>
            <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <!-- Description Field -->
        <div>
            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                Description
            </label>
            <textarea id="description" 
                      name="description" 
                      rows="3"
                      class="material-textarea <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> material-textarea-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                      placeholder="Enter role description"><?php echo e(old('description')); ?></textarea>
            <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <!-- Permissions Field -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
                Permissions <span class="text-red-500">*</span>
            </label>
            <div class="mb-2">
                <select id="permission-select" name="permissions[]" class="select2-multiple w-full" multiple>
                    <?php $__currentLoopData = $permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($permission->id); ?>" <?php echo e(in_array($permission->id, old('permissions', [])) ? 'selected' : ''); ?>>
                        <?php echo e($permission->name); ?> (<?php echo e($permission->slug); ?>)
                    </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <?php $__errorArgs = ['permissions'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            <a href="<?php echo e(route('admin.roles.index')); ?>" class="material-button material-button-sm material-button-secondary flex items-center">
                Cancel
            </a>
            <button type="submit" class="material-button material-button-sm material-button-primary flex items-center">
                <i class="material-icons mr-2 text-sm">save</i>
                Create Role
            </button>
        </div>
    </form>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://code.jquery.com/jquery-3.7.0.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize Select2
        $('#permission-select').select2({
            placeholder: 'Select permissions',
            allowClear: true,
            width: '100%'
        });
        // Form validation
        $("#role_form").validate({
            rules: {
                name: {
                    required: true,
                    minlength: 3,
                    maxlength: 255
                },
                "permissions[]": {
                    required: true,
                    minlength: 1
                }
            },
            messages: {
                name: {
                    required: "Please enter a role name",
                    minlength: "Role name must be at least 3 characters"
                },
                "permissions[]": {
                    required: "Please select at least one permission",
                    minlength: "Please select at least one permission"
                }
            },
            errorElement: 'span',
            errorPlacement: function (error, element) {
                if (element.attr("name") == "permissions[]") {
                    error.addClass("text-red-500 text-xs mt-1").insertAfter(element.next('.select2-container'));
                } else {
                    error.addClass("text-red-500 text-xs mt-1").insertAfter(element);
                }
            },
            highlight: function (element) {
                $(element).addClass('border-red-500').removeClass('border-gray-300');
                if ($(element).attr('name') == 'permissions[]') {
                    $(element).next('.select2-container').find('.select2-selection').addClass('border-red-500').removeClass('border-gray-300');
                }
            },
            unhighlight: function (element) {
                $(element).removeClass('border-red-500').addClass('border-gray-300');
                if ($(element).attr('name') == 'permissions[]') {
                    $(element).next('.select2-container').find('.select2-selection').removeClass('border-red-500').addClass('border-gray-300');
                }
            }
        });
    });
</script>
<?php $__env->stopPush(); ?>












<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\projects\client-admin-panel\resources\views/admin/roles/create.blade.php ENDPATH**/ ?>