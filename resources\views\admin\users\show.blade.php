@extends('layouts.admin')

@section('title', 'View User')

@php
    $pageTitle = 'User Details';
    $pageDescription = 'View user information and activity';
    $breadcrumbs = [
        ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
        ['title' => 'Users', 'url' => route('admin.users.index')],
        ['title' => 'View', 'url' => '#']
    ];
@endphp

@section('page-header')
<div class="flex items-center justify-between">
    <div>
        <h1 class="text-3xl font-semibold text-gray-900">{{ $pageTitle }}</h1>
        <p class="mt-2 text-gray-600">{{ $pageDescription }}</p>
    </div>
    <div class="flex space-x-3">
        <a href="{{ route('admin.users.edit', $user) }}" class="material-button material-button-primary flex items-center">
            <i class="material-icons mr-2">edit</i>
            Edit User
        </a>
        <a href="{{ route('admin.users.index') }}" class="material-button material-button-secondary flex items-center">
            <i class="material-icons mr-2">arrow_back</i>
            Back to Users
        </a>
    </div>
</div>
@endsection

@section('content')

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- User Profile Card -->
    <div class="lg:col-span-1">
        <div class="material-card p-6">
            <div class="text-center">
                <div class="w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-white font-bold text-2xl">{{ substr($user->name, 0, 1) }}</span>
                </div>
                <h3 class="text-xl font-semibold text-gray-900">{{ $user->name }}</h3>
                <p class="text-gray-600">{{ $user->email }}</p>
                
                <!-- Status Badges -->
                <div class="flex justify-center space-x-2 mt-4">
                    @if($user->email_verified_at)
                        <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            <i class="material-icons mr-1 text-xs">verified</i>
                            Verified
                        </span>
                    @else
                        <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                            <i class="material-icons mr-1 text-xs">pending</i>
                            Unverified
                        </span>
                    @endif
                    
                    @if(str_contains($user->email, 'admin'))
                        <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                            <i class="material-icons mr-1 text-xs">admin_panel_settings</i>
                            Admin
                        </span>
                    @endif
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="mt-6 space-y-3">
                <a href="{{ route('admin.users.edit', $user) }}"
                   class="w-full material-button material-button-sm material-button-secondary flex items-center justify-center">
                    <i class="material-icons mr-2 text-sm">edit</i>
                    Edit Profile
                </a>

                @if($user->id !== auth()->id())
                    <form method="POST" action="{{ route('admin.users.destroy', $user) }}"
                          onsubmit="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit"
                                class="w-full material-button material-button-sm material-button-danger flex items-center justify-center">
                            <i class="material-icons mr-2 text-sm">delete</i>
                            Delete User
                        </button>
                    </form>
                @endif
            </div>
        </div>
    </div>

    <!-- User Information -->
    <div class="lg:col-span-2 space-y-6">
        <!-- Basic Information -->
        <div class="material-card">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="material-icons mr-2">person</i>
                    Basic Information
                </h3>
            </div>
            <div class="p-6">
                <dl class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Full Name</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $user->name }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Email Address</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $user->email }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Account Type</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            @if(str_contains($user->email, 'admin'))
                                <span class="text-purple-600 font-medium">Administrator</span>
                            @else
                                <span class="text-gray-600">Regular User</span>
                            @endif
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Email Verification</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            @if($user->email_verified_at)
                                <span class="text-green-600">Verified on {{ $user->email_verified_at->format('M d, Y') }}</span>
                            @else
                                <span class="text-red-600">Not verified</span>
                            @endif
                        </dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Account Activity -->
        <div class="material-card">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="material-icons mr-2">schedule</i>
                    Account Activity
                </h3>
            </div>
            <div class="p-6">
                <dl class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Account Created</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {{ $user->created_at->format('M d, Y \a\t g:i A') }}
                            <span class="text-gray-500">({{ $user->created_at->diffForHumans() }})</span>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {{ $user->updated_at->format('M d, Y \a\t g:i A') }}
                            <span class="text-gray-500">({{ $user->updated_at->diffForHumans() }})</span>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">User ID</dt>
                        <dd class="mt-1 text-sm text-gray-900 font-mono">#{{ $user->id }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Account Status</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                Active
                            </span>
                        </dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="material-card">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="material-icons mr-2">info</i>
                    Additional Information
                </h3>
            </div>
            <div class="p-6">
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Account Summary</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• User has been active for {{ $user->created_at->diffForHumans(null, true) }}</li>
                        @if($user->email_verified_at)
                            <li>• Email address is verified and secure</li>
                        @else
                            <li>• Email verification is pending</li>
                        @endif
                        @if(str_contains($user->email, 'admin'))
                            <li>• Has administrative privileges</li>
                        @endif
                        @if($user->id === auth()->id())
                            <li>• This is your own account</li>
                        @endif
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection


