@extends('layouts.auth')

@section('title', 'Verify Email')

@section('header', 'Verify your email')
@section('subheader', 'Please check your inbox for a verification link')

@section('content')
<div class="material-card mb-6">
    <div class="flex items-start mb-4">
        <div class="flex-shrink-0">
            <i class="material-icons-round text-blue-500 text-2xl">info</i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">Verification Required</h3>
            <div class="mt-2 text-sm text-blue-700">
                <p>
                    Thanks for signing up! Before getting started, could you verify your email address by clicking on the link we just emailed to you? If you didn't receive the email, we will gladly send you another.
                </p>
            </div>
        </div>
    </div>
</div>

@if (session('status') == 'verification-link-sent')
    <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
        <div class="flex items-center">
            <i class="material-icons-round mr-2">check_circle</i>
            <span>A new verification link has been sent to the email address you provided during registration.</span>
        </div>
    </div>
@endif

@if (session('error'))
    <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <div class="flex items-center">
            <i class="material-icons-round mr-2">error</i>
            <span>{{ session('error') }}</span>
        </div>
    </div>
@endif

<div class="flex flex-col space-y-4">
    <form method="POST" action="{{ route('verification.send') }}">
        @csrf
        <button type="submit" class="w-full material-button material-button-primary flex justify-center items-center">
            <i class="material-icons-round mr-2">send</i>
            Resend Verification Email
        </button>
    </form>

    <form method="POST" action="{{ route('logout') }}">
        @csrf
        <button type="submit" class="w-full py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex justify-center items-center">
            <i class="material-icons-round mr-2">logout</i>
            Log Out
        </button>
    </form>
</div>
@endsection
