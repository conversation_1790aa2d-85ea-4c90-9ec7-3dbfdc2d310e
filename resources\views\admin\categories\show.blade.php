@extends('layouts.admin')

@section('title', 'View Category')

@push('styles')
<style>
    /* Custom prose styles for CKEditor content */
    .ck-content-display {
        line-height: 1.6;
    }
    .ck-content-display h1 {
        font-size: 1.875rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: #1f2937;
    }
    .ck-content-display h2 {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.75rem;
        color: #1f2937;
    }
    .ck-content-display h3 {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #1f2937;
    }
    .ck-content-display h4 {
        font-size: 1.125rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #1f2937;
    }
    .ck-content-display p {
        margin-bottom: 1rem;
    }
    .ck-content-display ul, .ck-content-display ol {
        margin-bottom: 1rem;
        padding-left: 1.5rem;
    }
    .ck-content-display li {
        margin-bottom: 0.25rem;
    }
    .ck-content-display blockquote {
        border-left: 4px solid #e5e7eb;
        padding-left: 1rem;
        margin: 1rem 0;
        font-style: italic;
        color: #6b7280;
    }
    .ck-content-display table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 1rem;
    }
    .ck-content-display th, .ck-content-display td {
        border: 1px solid #e5e7eb;
        padding: 0.5rem;
        text-align: left;
    }
    .ck-content-display th {
        background-color: #f9fafb;
        font-weight: 600;
    }
    .ck-content-display a {
        color: #3b82f6;
        text-decoration: underline;
    }
    .ck-content-display a:hover {
        color: #1d4ed8;
    }
    .ck-content-display strong {
        font-weight: 600;
    }
    .ck-content-display em {
        font-style: italic;
    }
</style>
@endpush

@php
    $pageTitle = 'Category Details';
    $pageDescription = 'View category information and SEO settings';
    $breadcrumbs = [
        ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
        ['title' => 'Categories', 'url' => route('admin.categories.index')],
        ['title' => $category->name, 'url' => '#'],
    ];
@endphp

@section('page-header')
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-semibold text-gray-900">{{ $pageTitle }}</h1>
            <p class="mt-2 text-gray-600">{{ $pageDescription }}</p>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.categories.edit', $category) }}" class="material-button material-button-primary flex items-center">
                <i class="material-icons mr-2">edit</i>
                Edit Category
            </a>
            <a href="{{ route('admin.categories.index') }}" class="material-button material-button-secondary flex items-center">
                <i class="material-icons mr-2">arrow_back</i>
                Back to Categories
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="material-card">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Category Name</label>
                            <p class="text-gray-900 font-semibold">{{ $category->name }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">URL Slug</label>
                            <p class="text-gray-600 font-mono text-sm">{{ $category->slug }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <span class="status-badge {{ $category->status === 'active' ? 'verified' : 'unverified' }}">
                                {{ ucfirst($category->status) }}
                            </span>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Featured</label>
                            <span class="status-badge {{ $category->is_featured ? 'admin' : 'default' }}">
                                {{ $category->is_featured ? 'Yes' : 'No' }}
                            </span>
                        </div>
                        
                        @if($category->parent)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Parent Category</label>
                            <p class="text-blue-600 hover:text-blue-800">
                                <a href="{{ route('admin.categories.show', $category->parent) }}">
                                    {{ $category->parent->name }}
                                </a>
                            </p>
                        </div>
                        @endif
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Sort Order</label>
                            <p class="text-gray-900">{{ $category->sort_order }}</p>
                        </div>
                    </div>
                    
                    @if($category->description)
                    <div class="mt-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                            <div class="ck-content-display text-gray-900">
                                {!! $category->description !!}
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- SEO Meta Information -->
            <div class="material-card">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">SEO Meta Information</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Meta Title</label>
                            <p class="text-gray-900">{{ $category->meta_title ?: 'Not set (falls back to name)' }}</p>
                            @if($category->meta_title)
                                <p class="text-xs text-gray-500 mt-1">{{ strlen($category->meta_title) }} characters</p>
                            @endif
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Robots Meta</label>
                            <p class="text-gray-900 font-mono text-sm">{{ $category->robots_meta }}</p>
                        </div>
                    </div>
                    
                    @if($category->meta_description)
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Meta Description</label>
                        <p class="text-gray-900">{{ $category->meta_description }}</p>
                        <p class="text-xs text-gray-500 mt-1">{{ strlen($category->meta_description) }} characters</p>
                    </div>
                    @endif
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                        @if($category->meta_keywords)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Meta Keywords</label>
                            <p class="text-gray-900">{{ $category->meta_keywords }}</p>
                        </div>
                        @endif
                        
                        @if($category->canonical_url)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Canonical URL</label>
                            <a href="{{ $category->canonical_url }}" target="_blank" class="text-blue-600 hover:text-blue-800 break-all">
                                {{ $category->canonical_url }}
                            </a>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Open Graph Information -->
            <div class="material-card">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Open Graph (Social Media)</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">OG Title</label>
                            <p class="text-gray-900">{{ $category->og_title ?: 'Not set (falls back to meta title)' }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">OG Type</label>
                            <p class="text-gray-900">{{ $category->og_type }}</p>
                        </div>
                    </div>
                    
                    @if($category->og_description)
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">OG Description</label>
                        <p class="text-gray-900">{{ $category->og_description }}</p>
                    </div>
                    @endif
                    
                    @if($category->og_image)
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">OG Image</label>
                        <div class="mt-2">
                            <img src="{{ $category->og_image_url }}" alt="OG Image" 
                                class="h-32 w-auto rounded-lg border border-gray-300 object-cover">
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Child Categories -->
            @if($category->children->count() > 0)
            <div class="material-card">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Child Categories ({{ $category->children->count() }})</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($category->children as $child)
                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="font-medium text-gray-900">{{ $child->name }}</h4>
                                    <p class="text-sm text-gray-500">{{ $child->slug }}</p>
                                    <span class="status-badge {{ $child->status === 'active' ? 'verified' : 'unverified' }} mt-1">
                                        {{ ucfirst($child->status) }}
                                    </span>
                                </div>
                                <a href="{{ route('admin.categories.show', $child) }}" 
                                    class="text-blue-600 hover:text-blue-800">
                                    <i class="material-icons text-sm">visibility</i>
                                </a>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Actions -->
            <div class="material-card">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                    
                    <div class="space-y-3">
                        <a href="{{ route('admin.categories.edit', $category) }}" 
                            class="w-full material-button material-button-sm material-button-primary flex items-center justify-center">
                            <i class="material-icons mr-2 text-sm">edit</i>
                            Edit Category
                        </a>
                        
                        <form method="POST" action="{{ route('admin.categories.destroy', $category) }}" 
                            onsubmit="return confirm('Are you sure you want to delete this category? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" 
                                class="w-full material-button material-button-sm material-button-danger flex items-center justify-center">
                                <i class="material-icons mr-2 text-sm">delete</i>
                                Delete Category
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Category Stats -->
            <div class="material-card">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Category Statistics</h3>
                    
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Child Categories</span>
                            <span class="font-medium text-gray-900">{{ $category->children->count() }}</span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Created</span>
                            <span class="font-medium text-gray-900">{{ $category->created_at->format('M d, Y') }}</span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Last Updated</span>
                            <span class="font-medium text-gray-900">{{ $category->updated_at->format('M d, Y') }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- SEO Preview -->
            <div class="material-card">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">SEO Preview</h3>
                    
                    <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <h4 class="text-blue-600 text-lg font-medium hover:underline cursor-pointer">
                            {{ $category->effective_meta_title }}
                        </h4>
                        <p class="text-green-600 text-sm mt-1">
                            example.com/categories/{{ $category->slug }}
                        </p>
                        @if($category->meta_description)
                        <p class="text-gray-700 text-sm mt-2">
                            {{ $category->meta_description }}
                        </p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
