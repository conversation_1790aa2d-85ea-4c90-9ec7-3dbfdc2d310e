<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserLoginLog extends Model
{
    protected $fillable = [
        'user_id',
        'email',
        'type',
        'ip_address',
        'user_agent',
        'session_id',
        'failure_reason',
        'metadata',
    ];

    protected $casts = [
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that owns the login log
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for successful logins
     */
    public function scopeSuccessful($query)
    {
        return $query->where('type', 'success');
    }

    /**
     * Scope for failed logins
     */
    public function scopeFailed($query)
    {
        return $query->where('type', 'failed');
    }

    /**
     * Scope for locked attempts
     */
    public function scopeLocked($query)
    {
        return $query->where('type', 'locked');
    }

    /**
     * Scope for deactivated attempts
     */
    public function scopeDeactivated($query)
    {
        return $query->where('type', 'deactivated');
    }

    /**
     * Scope for recent logs
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Get formatted user agent
     */
    public function getFormattedUserAgentAttribute()
    {
        if (!$this->user_agent) {
            return 'Unknown';
        }

        // Extract browser and OS info
        $userAgent = $this->user_agent;

        // Simple browser detection
        if (strpos($userAgent, 'Chrome') !== false) {
            $browser = 'Chrome';
        } elseif (strpos($userAgent, 'Firefox') !== false) {
            $browser = 'Firefox';
        } elseif (strpos($userAgent, 'Safari') !== false) {
            $browser = 'Safari';
        } elseif (strpos($userAgent, 'Edge') !== false) {
            $browser = 'Edge';
        } else {
            $browser = 'Unknown';
        }

        return $browser;
    }

    /**
     * Get type badge color
     */
    public function getTypeBadgeColorAttribute()
    {
        return match($this->type) {
            'success' => 'bg-green-100 text-green-800',
            'failed' => 'bg-red-100 text-red-800',
            'locked' => 'bg-yellow-100 text-yellow-800',
            'deactivated' => 'bg-gray-100 text-gray-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Log a login attempt
     */
    public static function logAttempt(
        ?User $user,
        string $email,
        string $type,
        string $ip,
        ?string $userAgent = null,
        ?string $sessionId = null,
        ?string $failureReason = null,
        ?array $metadata = null
    ) {
        return static::create([
            'user_id' => $user?->id,
            'email' => $email,
            'type' => $type,
            'ip_address' => $ip,
            'user_agent' => $userAgent,
            'session_id' => $sessionId,
            'failure_reason' => $failureReason,
            'metadata' => $metadata,
        ]);
    }
}
