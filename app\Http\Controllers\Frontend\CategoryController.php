<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    public function show($slug)
    {
        $category = Category::where('slug', $slug)->firstOrFail();
        $news = $category->news()->published()->latest()->paginate(12);
        
        return view('frontend.categories.show', compact('category', 'news'));
    }
}