<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Password;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use App\Services\LoginSecurityService;

class AuthController extends Controller
{
    /**
     * Show the login form.
     */
    public function showLoginForm()
    {
        return view('auth.login');
    }

    /**
     * Handle a login request to the application.
     */
    public function login(Request $request, LoginSecurityService $loginSecurity)
    {
        $request->validate([
            'email' => ['required', 'string', 'email'],
            'password' => ['required', 'string'],
        ]);

        $email = $request->input('email');
        $password = $request->input('password');
        $remember = $request->boolean('remember');
        $ip = $request->ip();
        $userAgent = $request->userAgent();

        // Use security service to attempt login
        $result = $loginSecurity->attemptLogin($email, $password, $ip, $userAgent);

        if (!$result['success']) {
            // Handle different types of failures
            switch ($result['type']) {
                case 'deactivated':
                case 'deactivated_after_failed':
                    return back()->withErrors([
                        'email' => $result['message']
                    ])->withInput($request->except('password'));

                case 'locked':
                case 'locked_after_failed':
                    return back()->withErrors([
                        'email' => $result['message']
                    ])->withInput($request->except('password'));

                case 'invalid_credentials':
                default:
                    return back()->withErrors([
                        'email' => $result['message']
                    ])->withInput($request->except('password'));
            }
        }

        // Successful login
        $user = $result['user'];

        // Log the user in
        Auth::login($user, $remember);
        $request->session()->regenerate();

        // Set password confirmation timestamp when user logs in
        $request->session()->put('auth.password_confirmed_at', time());

        // Redirect to admin dashboard
        return redirect()->route('admin.dashboard');
    }

    /**
     * Log the user out of the application.
     */
    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/');
    }

    /**
     * Show the form for requesting a password reset link.
     */
    public function showForgotPasswordForm()
    {
        return view('auth.forgot-password');
    }

    /**
     * Send a reset link to the given user.
     */
    public function sendResetLinkEmail(Request $request)
    {
        $request->validate([
            'email' => ['required', 'email'],
        ]);

        $status = Password::sendResetLink(
            $request->only('email')
        );

        return $status === Password::RESET_LINK_SENT
                    ? back()->with(['status' => __($status)])
                    : back()->withErrors(['email' => __($status)]);
    }

    /**
     * Display the password reset view.
     */
    public function showResetForm(Request $request)
    {
        return view('auth.reset-password', ['request' => $request]);
    }

    /**
     * Reset the given user's password.
     */
    public function resetPassword(Request $request)
    {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email',
            'password' => 'required|min:8|confirmed',
        ]);

        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function ($user, $password) {
                $user->forceFill([
                    'password' => Hash::make($password)
                ])->setRememberToken(Str::random(60));

                $user->save();

                event(new PasswordReset($user));
            }
        );

        return $status === Password::PASSWORD_RESET
                    ? redirect()->route('login')->with('status', __($status))
                    : back()->withErrors(['email' => [__($status)]]);
    }

    /**
     * Check if the user is an admin.
     */
    protected function isAdmin($user)
    {
        return str_contains($user->email, 'admin') || 
               (method_exists($user, 'hasRole') && $user->hasRole('admin'));
    }
}