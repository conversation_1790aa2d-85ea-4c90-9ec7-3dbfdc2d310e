<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;

class Tag extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'color',
        'status',
        'sort_order',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'canonical_url',
        'robots_meta',
        'og_title',
        'og_description',
        'og_image',
        'og_type',
    ];

    protected $casts = [
        'sort_order' => 'integer',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($tag) {
            if (empty($tag->slug)) {
                $tag->slug = Str::slug($tag->name);
            }
        });

        static::updating(function ($tag) {
            if ($tag->isDirty('name') && empty($tag->slug)) {
                $tag->slug = Str::slug($tag->name);
            }
        });
    }

    /**
     * Get the OG image URL
     */
    public function getOgImageUrlAttribute()
    {
        if ($this->og_image) {
            return asset('storage/' . $this->og_image);
        }

        return "data:image/svg+xml,%3csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100' height='100' fill='%23f3f4f6'/%3e%3ctext x='50%25' y='50%25' font-size='14' text-anchor='middle' dy='.3em' fill='%236b7280'%3eNo Image%3c/text%3e%3c/svg%3e";
    }

    /**
     * Scope for active tags
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Get the effective meta title (falls back to name if not set)
     */
    public function getEffectiveMetaTitleAttribute()
    {
        return $this->meta_title ?: $this->name;
    }

    /**
     * Get the effective OG title (falls back to meta_title or name)
     */
    public function getEffectiveOgTitleAttribute()
    {
        return $this->og_title ?: $this->effective_meta_title;
    }

    /**
     * Get the color with opacity for background
     */
    public function getColorWithOpacityAttribute()
    {
        // Convert hex to rgba with 0.1 opacity
        $hex = ltrim($this->color, '#');
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));

        return "rgba($r, $g, $b, 0.1)";
    }

    /**
     * The news that belong to the tag.
     */
    public function news(): BelongsToMany
    {
        return $this->belongsToMany(News::class, 'news_tag');
    }
}
