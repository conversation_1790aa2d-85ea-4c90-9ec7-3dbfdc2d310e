@extends('layouts.auth')

@section('title', 'Reset Password')

@section('header', 'Reset your password')
@section('subheader', 'Create a new secure password for your account')

@section('content')
<!-- Reset Password Form -->
<form class="space-y-6" method="POST" action="{{ route('password.update') }}">
    @csrf

    <!-- Password Reset Token -->
    <input type="hidden" name="token" value="{{ $request->route('token') }}">

    <!-- Email Field -->
    <div>
        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
            Email address
        </label>
        <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                <i class="material-icons-round text-gray-400 text-lg">email</i>
            </div>
            <input id="email"
                   name="email"
                   type="email"
                   autocomplete="email"
                   required
                   value="{{ old('email', $request->email) }}"
                   class="material-input-with-icon @error('email') material-input-error @enderror"
                   placeholder="Enter your email address">
        </div>
        @error('email')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
    </div>

    <!-- Password Field -->
    <div>
        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
            New password
        </label>
        <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                <i class="material-icons-round text-gray-400 text-lg">lock</i>
            </div>
            <input id="password"
                   name="password"
                   type="password"
                   autocomplete="new-password"
                   required
                   class="material-input-with-icon @error('password') material-input-error @enderror"
                   placeholder="Create a new password">
            <button type="button"
                    id="toggle-password-reset"
                    data-password-toggle="password:password-icon-reset"
                    class="absolute inset-y-0 right-0 px-3 flex items-center z-10 focus:outline-none rounded">
                <i class="material-icons-round text-gray-400 hover:text-gray-600 text-lg" id="password-icon-reset">visibility</i>
            </button>
        </div>
        @error('password')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
    </div>

    <!-- Confirm Password Field -->
    <div>
        <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">
            Confirm new password
        </label>
        <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                <i class="material-icons-round text-gray-400 text-lg">lock</i>
            </div>
            <input id="password_confirmation"
                   name="password_confirmation"
                   type="password"
                   autocomplete="new-password"
                   required
                   class="material-input-with-icon"
                   placeholder="Confirm your new password">
            <button type="button"
                    id="toggle-password-confirm-reset"
                    data-password-toggle="password_confirmation:password-confirm-icon-reset"
                    class="absolute inset-y-0 right-0 px-3 flex items-center z-10 focus:outline-none rounded">
                <i class="material-icons-round text-gray-400 hover:text-gray-600 text-lg" id="password-confirm-icon-reset">visibility</i>
            </button>
        </div>
    </div>

    <!-- Submit Button -->
    <div>
        <button type="submit" class="w-full material-button material-button-md material-button-primary flex justify-center items-center">
            <i class="material-icons-round mr-2">lock_reset</i>
            Reset password
        </button>
    </div>
</form>

<!-- Back to Login Link -->
<div class="mt-6 text-center">
    <p class="text-sm text-gray-600">
        Remember your password?
        <a href="{{ route('login') }}" class="font-medium text-blue-600 hover:text-blue-500 transition-colors duration-200">
            Back to login
        </a>
    </p>
</div>
@endsection

@push('scripts')
<!-- No inline JavaScript needed anymore -->
@endpush




