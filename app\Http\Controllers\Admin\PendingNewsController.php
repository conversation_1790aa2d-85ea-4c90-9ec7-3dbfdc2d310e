<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\News;
use App\Models\User;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use App\Helpers\DataTableHelper;

class PendingNewsController extends Controller
{
    /**
     * Display a listing of pending news.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $query = News::with(['categories', 'tags', 'approver'])
                ->pendingApproval()
                ->latest();

            return DataTables::of($query)
                ->addColumn('news', function ($news) {
                    $imageHtml = '<img src="' . $news->main_image_url . '" alt="' . $news->title . '" class="w-12 h-12 object-cover rounded-lg mr-3">';
                    $titleHtml = '<div class="font-medium text-gray-900">' . $news->title . '</div>';
                    $slugHtml = '<div class="text-sm text-gray-500">' . $news->slug . '</div>';
                    
                    return '<div class="flex items-center">' . $imageHtml . '<div>' . $titleHtml . $slugHtml . '</div></div>';
                })
                ->addColumn('categories', function ($news) {
                    return DataTableHelper::relatedItemsList($news->categories);
                })
                ->addColumn('tags', function ($news) {
                    return DataTableHelper::relatedItemsList($news->tags);
                })
                ->addColumn('approval_status', function ($news) {
                    $statusClasses = [
                        'pending' => 'bg-yellow-100 text-yellow-800',
                        'approved' => 'bg-green-100 text-green-800',
                        'rejected' => 'bg-red-100 text-red-800'
                    ];
                    
                    $class = $statusClasses[$news->approval_status] ?? 'bg-gray-100 text-gray-800';
                    
                    return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ' . $class . '">' . 
                           ucfirst($news->approval_status) . '</span>';
                })
                ->addColumn('created_at', function ($news) {
                    return $news->created_at->format('M d, Y H:i');
                })
                ->addColumn('actions', function ($news) {
                    $actions = '<div class="flex items-center space-x-2">';
                    
                    // View action
                    $actions .= '<a href="' . route('admin.pending-news.show', $news) . '" 
                                   class="action-btn view" title="View Details">
                                   <i class="material-icons text-sm">visibility</i>
                                </a>';
                    
                    // Approve action
                    $actions .= '<button onclick="approveNews(' . $news->id . ')" 
                                   class="action-btn approve" title="Approve">
                                   <i class="material-icons text-sm">check_circle</i>
                                </button>';
                    
                    // Reject action
                    $actions .= '<button onclick="rejectNews(' . $news->id . ')" 
                                   class="action-btn reject" title="Reject">
                                   <i class="material-icons text-sm">cancel</i>
                                </button>';
                    
                    // Edit action
                    $actions .= '<a href="' . route('admin.news.edit', $news) . '" 
                                   class="action-btn edit" title="Edit">
                                   <i class="material-icons text-sm">edit</i>
                                </a>';
                    
                    $actions .= '</div>';
                    return $actions;
                })
                ->rawColumns(['news', 'categories', 'tags', 'approval_status', 'actions'])
                ->make(true);
        }
        
        return view('admin.pending-news.index');
    }

    /**
     * Display the specified pending news.
     */
    public function show(News $news)
    {
        // Load relationships
        $news->load(['categories', 'tags', 'approver']);
        
        return view('admin.pending-news.show', compact('news'));
    }

    /**
     * Approve the specified news.
     */
    public function approve(Request $request, News $news)
    {
        try {
            $news->update([
                'approval_status' => 'approved',
                'approved_at' => now(),
                'approved_by' => auth()->id(),
                'status' => 'active', // Make it active when approved
                'published_at' => now(), // Set published date
            ]);

            return response()->json([
                'success' => true,
                'message' => 'News approved successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to approve news: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reject the specified news.
     */
    public function reject(Request $request, News $news)
    {
        $request->validate([
            'reason' => 'required|string|max:1000'
        ]);

        try {
            $news->update([
                'approval_status' => 'rejected',
                'approved_at' => now(),
                'approved_by' => auth()->id(),
                'rejection_reason' => $request->reason,
                'status' => 'inactive', // Keep inactive when rejected
            ]);

            return response()->json([
                'success' => true,
                'message' => 'News rejected successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reject news: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk approve multiple news items.
     */
    public function bulkApprove(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:news,id'
        ]);

        try {
            News::whereIn('id', $request->ids)
                ->where('approval_status', 'pending')
                ->update([
                    'approval_status' => 'approved',
                    'approved_at' => now(),
                    'approved_by' => auth()->id(),
                    'status' => 'active',
                    'published_at' => now(),
                ]);

            return response()->json([
                'success' => true,
                'message' => 'Selected news items approved successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to approve news items: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get pending news statistics.
     */
    public function getStats()
    {
        $stats = [
            'pending' => News::pendingApproval()->count(),
            'approved_today' => News::approved()
                ->whereDate('approved_at', today())
                ->count(),
            'rejected_today' => News::rejected()
                ->whereDate('approved_at', today())
                ->count(),
            'total_generated' => News::whereNotNull('approval_status')->count(),
        ];

        return response()->json($stats);
    }
}
