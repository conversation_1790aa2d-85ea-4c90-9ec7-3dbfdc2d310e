<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('login_attempts', function (Blueprint $table) {
            $table->id();
            $table->string('email')->index();
            $table->string('ip_address', 45)->index();
            $table->integer('failed_attempts')->default(0);
            $table->timestamp('locked_until')->nullable()->index();
            $table->enum('status', ['active', 'locked', 'deactivated'])->default('active')->index();
            $table->integer('lockout_level')->default(0); // 0=none, 1=10min, 2=15min, 3=deactivated
            $table->timestamp('last_attempt_at')->nullable();
            $table->timestamp('created_at');
            $table->timestamp('updated_at');

            // Unique constraint to prevent duplicates
            $table->unique(['email', 'ip_address']);

            // Indexes for performance
            $table->index(['email', 'status']);
            $table->index(['ip_address', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('login_attempts');
    }
};
