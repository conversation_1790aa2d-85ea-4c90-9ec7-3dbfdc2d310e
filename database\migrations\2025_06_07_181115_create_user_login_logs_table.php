<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_login_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('email')->index(); // Store email even if user is deleted
            $table->enum('type', ['success', 'failed', 'locked', 'deactivated'])->index();
            $table->string('ip_address', 45)->index(); // IPv6 support
            $table->text('user_agent')->nullable();
            $table->string('session_id')->nullable();
            $table->text('failure_reason')->nullable(); // Why login failed
            $table->json('metadata')->nullable(); // Additional data
            $table->timestamp('created_at')->index();
            $table->timestamp('updated_at')->nullable();

            // Indexes for performance
            $table->index(['user_id', 'type', 'created_at']);
            $table->index(['email', 'type', 'created_at']);
            $table->index(['ip_address', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_login_logs');
    }
};
