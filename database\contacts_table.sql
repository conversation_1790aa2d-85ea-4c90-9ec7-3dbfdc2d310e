-- Create contacts table
CREATE TABLE IF NOT EXISTS `contacts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `subject` enum('general','news-tip','press','partnership','technical','feedback','other') NOT NULL,
  `message` text NOT NULL,
  `status` enum('new','in_progress','responded','closed') NOT NULL DEFAULT 'new',
  `responded_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `contacts_status_created_at_index` (`status`,`created_at`),
  KEY `contacts_subject_created_at_index` (`subject`,`created_at`),
  KEY `contacts_email_index` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample data (optional)
INSERT INTO `contacts` (`name`, `email`, `phone`, `subject`, `message`, `status`, `created_at`, `updated_at`) VALUES
('John Doe', '<EMAIL>', '+****************', 'general', 'Hello, I have a question about your news coverage. Can you provide more information about your editorial process?', 'new', NOW(), NOW()),
('Jane Smith', '<EMAIL>', NULL, 'news-tip', 'I have information about a local story that might interest your readers. There was an incident at the city hall yesterday that hasn\'t been covered yet.', 'in_progress', NOW(), NOW()),
('Mike Johnson', '<EMAIL>', '+****************', 'press', 'I\'m a journalist from another publication and would like to discuss a potential collaboration on a story we\'re both covering.', 'responded', NOW(), NOW());
