@extends('layouts.admin')

@section('title', 'Edit Role')

@php
    $pageTitle = 'Edit Role';
    $pageDescription = 'Update role information and permissions';
    $breadcrumbs = [
        ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
        ['title' => 'Roles', 'url' => route('admin.roles.index')],
        ['title' => 'Edit', 'url' => '#']
    ];
@endphp

@section('page-header')
<div class="flex items-center justify-between">
    <div>
        <h1 class="text-3xl font-semibold text-gray-900">{{ $pageTitle }}</h1>
        <p class="mt-2 text-gray-600">{{ $pageDescription }} for {{ $role->name }}</p>
    </div>
    <div class="flex space-x-3">
        <a href="{{ route('admin.roles.show', $role) }}" class="material-button material-button-md material-button-secondary flex items-center">
            <i class="material-icons mr-2">visibility</i>
            View Role
        </a>
        <a href="{{ route('admin.roles.index') }}" class="material-button material-button-md material-button-secondary flex items-center">
            <i class="material-icons mr-2">arrow_back</i>
            Back to Roles
        </a>
    </div>
</div>
@endsection

@section('content')
<!-- Edit Role Form -->
<div class="material-card">
    <form method="POST" action="{{ route('admin.roles.update', $role) }}" class="p-6 space-y-6" id="role_edit_form">
        @csrf
        @method('PUT')

        <!-- Name Field -->
        <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                Role Name <span class="text-red-500">*</span>
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                    <i class="material-icons text-gray-400 text-lg">badge</i>
                </div>
                <input id="name" 
                       name="name" 
                       type="text" 
                       required 
                       value="{{ old('name', $role->name) }}"
                       class="material-input-with-icon @error('name') border-red-500 focus:ring-red-500 @enderror"
                       placeholder="Enter role name"
                       {{ $role->slug === 'admin' ? 'readonly' : '' }}>
            </div>
            @error('name')
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
            @enderror
        </div>

        <!-- Description Field -->
        <div>
            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                Description
            </label>
            <textarea id="description" 
                      name="description" 
                      rows="3"
                      class="material-input @error('description') border-red-500 focus:ring-red-500 @enderror"
                      placeholder="Enter role description">{{ old('description', $role->description) }}</textarea>
            @error('description')
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
            @enderror
        </div>

        <!-- Permissions Field -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
                Permissions <span class="text-red-500">*</span>
            </label>
            <div class="mb-2">
                <select id="permission-select" name="permissions[]" class="select2-multiple w-full" multiple>
                    @foreach($permissions as $permission)
                    <option value="{{ $permission->id }}" {{ in_array($permission->id, old('permissions', $role->permissions->pluck('id')->toArray())) ? 'selected' : '' }} >
                        {{ $permission->name }} ({{ $permission->slug }})
                    </option>
                    @endforeach
                </select>
            </div>
            @error('permissions')
                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
            @enderror
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            <a href="{{ route('admin.roles.index') }}" class="material-button material-button-sm material-button-secondary flex items-center">
                Cancel
            </a>
            <button type="submit" class="material-button material-button-sm material-button-primary flex items-center">
                <i class="material-icons mr-2 text-sm">save</i>
                Update Role
            </button>
        </div>
    </form>
</div>
@endsection

@push('styles')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
</style>
@endpush

@push('scripts')
<script src="https://code.jquery.com/jquery-3.7.0.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize Select2
        $('#permission-select').select2({
            placeholder: 'Select permissions',
            allowClear: true,
            width: '100%'
        });
        
        // Form validation
        $("#role_edit_form").validate({
            rules: {
                name: {
                    required: true,
                    minlength: 3,
                    maxlength: 255
                },
                "permissions[]": {
                    required: true,
                    minlength: 1
                }
            },
            messages: {
                name: {
                    required: "Please enter a role name",
                    minlength: "Role name must be at least 3 characters"
                },
                "permissions[]": {
                    required: "Please select at least one permission",
                    minlength: "Please select at least one permission"
                }
            },
            errorElement: 'span',
            errorPlacement: function (error, element) {
                if (element.attr("name") == "permissions[]") {
                    error.addClass("text-red-500 text-xs mt-1").insertAfter(element.next('.select2-container'));
                } else {
                    error.addClass("text-red-500 text-xs mt-1").insertAfter(element);
                }
            },
            highlight: function (element) {
                $(element).addClass('border-red-500').removeClass('border-gray-300');
                if ($(element).attr('name') == 'permissions[]') {
                    $(element).next('.select2-container').find('.select2-selection').addClass('border-red-500').removeClass('border-gray-300');
                }
            },
            unhighlight: function (element) {
                $(element).removeClass('border-red-500').addClass('border-gray-300');
                if ($(element).attr('name') == 'permissions[]') {
                    $(element).next('.select2-container').find('.select2-selection').removeClass('border-red-500').addClass('border-gray-300');
                }
            }
        });
    });
</script>
@endpush



