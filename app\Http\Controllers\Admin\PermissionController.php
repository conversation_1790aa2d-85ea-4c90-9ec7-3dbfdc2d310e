<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Permission;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use App\Helpers\DataTableHelper;

class PermissionController extends Controller
{
    /**
     * Display a listing of permissions and handle datatable requests.
     */
    public function index(Request $request)
    {
        // Check if this is an AJAX request for datatable
        if ($request->ajax()) {
            $query = Permission::with('roles');
            
            return DataTables::of($query)
                ->filter(function ($query) use ($request) {
                    if ($request->has('search') && $request->input('search.value')) {
                        $searchValue = $request->input('search.value');
                        $query->where(function ($q) use ($searchValue) {
                            $q->where('name', 'like', "%{$searchValue}%")
                                ->orWhere('slug', 'like', "%{$searchValue}%")
                                ->orWhere('description', 'like', "%{$searchValue}%");
                        });
                    }
                })
                ->addColumn('name', function ($permission) {
                    return '<div class="flex items-center ml-2">
                                <div>
                                    <div class="text-gray-900">' . $permission->name . '</div>
                                    <div class="text-xs text-gray-500 mt-1">' . $permission->slug . '</div>
                                </div>
                            </div>';
                })
                ->addColumn('roles', function ($permission) {
                    return DataTableHelper::relatedItemsList($permission->roles);
                })
                ->addColumn('actions', function ($permission) {
                    return DataTableHelper::actionsColumn($permission, 'admin.permissions', [
                        'viewTitle' => 'View Permission',
                        'editTitle' => 'Edit Permission',
                        'deleteTitle' => 'Delete Permission'
                    ]);
                })
                ->rawColumns(['name', 'roles', 'actions'])
                ->make(true);
        }

        return view('admin.permissions.index');
    }

    /**
     * Show the form for creating a new permission.
     */
    public function create()
    {
        return view('admin.permissions.create');
    }

    /**
     * Store a newly created permission in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:permissions,name'],
            'slug' => ['required', 'string', 'max:255', 'unique:permissions,slug', 'regex:/^[a-z0-9\-\.\_]+$/'],
            'description' => ['nullable', 'string', 'max:1000'],
        ]);

        Permission::create([
            'name' => $request->name,
            'slug' => $request->slug,
            'description' => $request->description,
        ]);

        return redirect()->route('admin.permissions.index')->with('success', 'Permission created successfully.');
    }

    /**
     * Display the specified permission.
     */
    public function show(Permission $permission)
    {
        $permission->load('roles');
        return view('admin.permissions.show', compact('permission'));
    }

    /**
     * Show the form for editing the specified permission.
     */
    public function edit(Permission $permission)
    {
        return view('admin.permissions.edit', compact('permission'));
    }

    /**
     * Update the specified permission in storage.
     */
    public function update(Request $request, Permission $permission)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:permissions,name,' . $permission->id],
            'slug' => ['required', 'string', 'max:255', 'unique:permissions,slug,' . $permission->id, 'regex:/^[a-z0-9\-\.\_]+$/'],
            'description' => ['nullable', 'string', 'max:1000'],
        ]);

        $permission->update([
            'name' => $request->name,
            'slug' => $request->slug,
            'description' => $request->description,
        ]);

        return redirect()->route('admin.permissions.index')->with('success', 'Permission updated successfully.');
    }

    /**
     * Remove the specified permission from storage.
     */
    public function destroy(Permission $permission)
    {
        // Detach the permission from all roles before deleting
        $permission->roles()->detach();
        $permission->delete();

        return redirect()->route('admin.permissions.index')->with('success', 'Permission deleted successfully.');
    }
}
