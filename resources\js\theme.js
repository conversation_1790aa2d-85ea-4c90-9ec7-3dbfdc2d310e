/**
 * Theme Management System
 * Handles light, dark, and system theme modes
 */

class ThemeManager {
    constructor() {
        this.themes = {
            LIGHT: 'light',
            DARK: 'dark',
            SYSTEM: 'system'
        };
        
        this.currentTheme = this.getStoredTheme() || this.themes.SYSTEM;
        this.init();
    }

    init() {
        // Apply initial theme
        this.applyTheme(this.currentTheme);

        // Initialize theme toggle functionality
        this.initThemeToggle();

        // Listen for system theme changes
        this.initSystemThemeListener();

        // Update theme icons
        this.updateThemeIcons();
    }

    getStoredTheme() {
        return localStorage.getItem('theme');
    }

    setStoredTheme(theme) {
        localStorage.setItem('theme', theme);
    }

    getSystemTheme() {
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? this.themes.DARK : this.themes.LIGHT;
    }

    applyTheme(theme) {
        const html = document.documentElement;

        // Remove existing theme classes
        html.classList.remove('light', 'dark');

        let effectiveTheme = theme;

        // If system theme, get the actual system preference
        if (theme === this.themes.SYSTEM) {
            effectiveTheme = this.getSystemTheme();
        }

        // Apply theme class
        html.classList.add(effectiveTheme);

        // Store the selected theme preference
        this.currentTheme = theme;
        this.setStoredTheme(theme);

        // Update theme icons
        this.updateThemeIcons();
    }

    updateThemeIcons() {
        const lightIcon = document.getElementById('theme-icon-light');
        const darkIcon = document.getElementById('theme-icon-dark');
        const systemIcon = document.getElementById('theme-icon-system');
        
        // Hide all icons first
        [lightIcon, darkIcon, systemIcon].forEach(icon => {
            if (icon) icon.classList.add('hidden');
        });
        
        // Show appropriate icon based on current theme
        switch (this.currentTheme) {
            case this.themes.LIGHT:
                if (lightIcon) lightIcon.classList.remove('hidden');
                break;
            case this.themes.DARK:
                if (darkIcon) darkIcon.classList.remove('hidden');
                break;
            case this.themes.SYSTEM:
                if (systemIcon) systemIcon.classList.remove('hidden');
                break;
        }
    }

    initThemeToggle() {
        // Theme dropdown functionality
        const themeDropdown = document.querySelector('[data-theme-dropdown]');

        if (!themeDropdown) {
            console.warn('Theme dropdown not found');
            return;
        }

        const themeButton = themeDropdown.querySelector('[data-theme-dropdown-button]');
        const themeMenu = themeDropdown.querySelector('[data-theme-dropdown-menu]');
        const themeOptions = themeDropdown.querySelectorAll('[data-theme-option]');

        if (!themeButton || !themeMenu) {
            console.warn('Theme button or menu not found');
            return;
        }

        let isOpen = false;

        // Toggle dropdown
        themeButton.addEventListener('click', (e) => {
            e.stopPropagation();
            isOpen = !isOpen;

            if (isOpen) {
                themeMenu.classList.remove('hidden');
            } else {
                themeMenu.classList.add('hidden');
            }
        });

        // Handle theme option clicks
        themeOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                e.stopPropagation();
                const selectedTheme = option.getAttribute('data-theme-option');
                this.applyTheme(selectedTheme);

                // Close dropdown
                isOpen = false;
                themeMenu.classList.add('hidden');
            });
        });

        // Close dropdown on outside click
        document.addEventListener('click', () => {
            if (isOpen) {
                isOpen = false;
                themeMenu.classList.add('hidden');
            }
        });
    }

    initSystemThemeListener() {
        // Listen for system theme changes
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        
        mediaQuery.addEventListener('change', () => {
            // Only update if current theme is system
            if (this.currentTheme === this.themes.SYSTEM) {
                this.applyTheme(this.themes.SYSTEM);
            }
        });
    }

    // Public method to change theme programmatically
    setTheme(theme) {
        if (Object.values(this.themes).includes(theme)) {
            this.applyTheme(theme);
        }
    }

    // Get current effective theme (resolves system to actual theme)
    getEffectiveTheme() {
        if (this.currentTheme === this.themes.SYSTEM) {
            return this.getSystemTheme();
        }
        return this.currentTheme;
    }
}

// Initialize theme manager when DOM is loaded
let themeManager;

export function initTheme() {
    themeManager = new ThemeManager();
    return themeManager;
}

// Export for global access
export { ThemeManager };

// Auto-initialize if DOM is already loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initTheme);
} else {
    // Add a small delay to ensure DOM elements are available
    setTimeout(initTheme, 100);
}

// Also make it globally available for debugging
window.initTheme = initTheme;
