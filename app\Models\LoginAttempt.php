<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LoginAttempt extends Model
{
    protected $fillable = [
        'email',
        'ip_address',
        'failed_attempts',
        'locked_until',
        'status',
        'lockout_level',
        'last_attempt_at',
    ];

    protected $casts = [
        'locked_until' => 'datetime',
        'last_attempt_at' => 'datetime',
    ];

    // Lockout durations in minutes
    const LOCKOUT_DURATIONS = [
        1 => 10,  // First lockout: 10 minutes
        2 => 15,  // Second lockout: 15 minutes
        3 => null, // Third lockout: permanent (deactivation)
    ];

    const MAX_ATTEMPTS_BEFORE_LOCKOUT = 5;

    /**
     * Check if this attempt record is currently locked
     */
    public function isLocked(): bool
    {
        return $this->status === 'locked' &&
               $this->locked_until &&
               $this->locked_until->isFuture();
    }

    /**
     * Check if this attempt record is deactivated
     */
    public function isDeactivated(): bool
    {
        return $this->status === 'deactivated';
    }

    /**
     * Get remaining lockout time in minutes
     */
    public function getRemainingLockoutMinutes(): int
    {
        if (!$this->isLocked()) {
            return 0;
        }

        // Calculate difference correctly - locked_until is in the future
        $minutes = now()->diffInMinutes($this->locked_until, false);
        return max(0, (int) $minutes);
    }

    /**
     * Get remaining lockout time formatted
     */
    public function getRemainingLockoutTimeAttribute(): string
    {
        if (!$this->isLocked()) {
            return '';
        }

        $minutes = $this->getRemainingLockoutMinutes();

        if ($minutes <= 0) {
            return '0 minutes';
        }

        if ($minutes >= 60) {
            $hours = floor($minutes / 60);
            $remainingMinutes = $minutes % 60;
            return $hours . 'h ' . $remainingMinutes . 'm';
        }

        return $minutes . ' minute' . ($minutes === 1 ? '' : 's');
    }

    /**
     * Record a failed login attempt
     */
    public static function recordFailedAttempt(string $email, string $ip): self
    {
        $attempt = static::firstOrCreate(
            ['email' => $email, 'ip_address' => $ip],
            [
                'failed_attempts' => 0,
                'status' => 'active',
                'lockout_level' => 0,
            ]
        );

        $attempt->increment('failed_attempts');
        $attempt->last_attempt_at = now();

        // Check if we need to apply lockout
        if ($attempt->failed_attempts >= self::MAX_ATTEMPTS_BEFORE_LOCKOUT) {
            $attempt->applyLockout();
        }

        $attempt->save();

        return $attempt;
    }

    /**
     * Apply lockout based on current level
     */
    public function applyLockout(): void
    {
        $this->lockout_level++;

        if ($this->lockout_level >= 3) {
            // Permanent deactivation
            $this->status = 'deactivated';
            $this->locked_until = null;
        } else {
            // Temporary lockout
            $duration = self::LOCKOUT_DURATIONS[$this->lockout_level];
            $this->status = 'locked';
            $this->locked_until = now()->addMinutes($duration);
        }

        // Reset failed attempts counter for next cycle
        $this->failed_attempts = 0;
    }

    /**
     * Clear the lockout (admin action or time expired)
     */
    public function clearLockout(): void
    {
        $this->status = 'active';
        $this->locked_until = null;
        $this->failed_attempts = 0;
    }

    /**
     * Reset all attempts (admin action)
     */
    public function resetAttempts(): void
    {
        $this->failed_attempts = 0;
        $this->lockout_level = 0;
        $this->status = 'active';
        $this->locked_until = null;
    }

    /**
     * Check if lockout has expired and clear it
     */
    public function checkAndClearExpiredLockout(): bool
    {
        if ($this->status === 'locked' &&
            $this->locked_until &&
            $this->locked_until->isPast()) {

            $this->status = 'active';
            $this->locked_until = null;
            $this->save();

            return true;
        }

        return false;
    }

    /**
     * Get lockout status message
     */
    public function getLockoutMessageAttribute(): string
    {
        if ($this->isDeactivated()) {
            return 'Your account has been deactivated due to multiple failed login attempts. Please contact administrators.';
        }

        if ($this->isLocked()) {
            $remainingTime = $this->remaining_lockout_time;
            $lockoutLevel = $this->lockout_level;
            if ($remainingTime === '0 minutes' || empty($remainingTime)) {
                return "Account is temporarily locked due to failed login attempts. Please try again in a few minutes.";
            }

            $levelText = match($lockoutLevel) {
                1 => "first security lockout",
                2 => "second security lockout",
                default => "security lockout"
            };

            return "Account is temporarily locked ({$levelText}). Try again in {$remainingTime}.";
        }

        $remaining = self::MAX_ATTEMPTS_BEFORE_LOCKOUT - $this->failed_attempts;

        if ($remaining <= 2) {
            return "Warning: {$remaining} login attempt" . ($remaining === 1 ? '' : 's') . " remaining before account lockout.";
        }

        return '';
    }

    /**
     * Scope for active (not locked/deactivated) attempts
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for locked attempts
     */
    public function scopeLocked($query)
    {
        return $query->where('status', 'locked');
    }

    /**
     * Scope for deactivated attempts
     */
    public function scopeDeactivated($query)
    {
        return $query->where('status', 'deactivated');
    }
}
