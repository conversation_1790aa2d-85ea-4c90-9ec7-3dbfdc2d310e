@extends('layouts.auth')

@section('title', 'Sign In')

@section('header', 'Welcome back')
@section('subheader', 'Sign in to your account to continue')

@section('content')
<!-- Login Form -->
<form class="space-y-6" method="POST" action="{{ route('login') }}">
    @csrf

    <!-- Error Messages -->
    @error('email')
        <div class="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {{ $message }}
        </div>
    @enderror

    <!-- Email Field -->
    <div>
        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
            Email address
        </label>
        <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                <i class="material-icons-round text-gray-400 text-lg">email</i>
            </div>
            <input id="email"
                   name="email"
                   type="email"
                   autocomplete="email"
                   required
                   value="{{ old('email') }}"
                   class="material-input-with-icon @error('email') material-input-error @enderror"
                   placeholder="Enter your email address">
        </div>
    </div>

    <!-- Password Field -->
    <div>
        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
            Password
        </label>
        <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                <i class="material-icons-round text-gray-400 text-lg">lock</i>
            </div>
            <input id="password"
                   name="password"
                   type="password"
                   autocomplete="current-password"
                   required
                   class="material-input-with-icon @error('password') material-input-error @enderror"
                   placeholder="Enter your password">
            <button type="button"
                    id="toggle-password-login"
                    data-password-toggle="password:password-icon-login"
                    class="absolute inset-y-0 right-0 px-3 flex items-center z-10 focus:outline-none rounded">
                <i class="material-icons-round text-gray-400 hover:text-gray-600 text-lg" id="password-icon-login">visibility</i>
            </button>
        </div>
        @error('password')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
    </div>

    <!-- Remember Me and Forgot Password -->
    <div class="flex items-center justify-between">
        <div class="flex items-center">
            <input id="remember_me"
                   name="remember"
                   type="checkbox"
                   class="h-4 w-4 text-blue-600 focus:outline-none border-gray-300 rounded">
            <label for="remember_me" class="ml-2 block text-sm text-gray-700">
                Remember me
            </label>
        </div>

        <div class="text-sm">
            <a href="{{ route('password.request') }}"
               class="font-medium text-blue-600 hover:text-blue-500 transition-colors duration-200">
                Forgot your password?
            </a>
        </div>
    </div>

    <!-- Submit Button -->
    <div>
        <button type="submit"
                class="w-full material-button material-button-md material-button-primary flex justify-center items-center">
            <i class="material-icons-round mr-2">login</i>
            Sign in
        </button>
    </div>
</form>

@endsection

@push('scripts')
<script>
    // Auto-refresh page if there's a lockout message to update remaining time
    document.addEventListener('DOMContentLoaded', function() {
        const errorDiv = document.querySelector('[class*="bg-orange-50"]');
        if (errorDiv && errorDiv.textContent.includes('locked')) {
            // Refresh every 30 seconds to update remaining time
            setTimeout(function() {
                window.location.reload();
            }, 30000);

            // Add a visual countdown indicator
            const countdownDiv = document.createElement('div');
            countdownDiv.className = 'mt-2 text-xs text-orange-600';
            countdownDiv.innerHTML = '<i class="material-icons text-xs mr-1">schedule</i>Page will refresh in 30 seconds to update remaining time...';
            errorDiv.appendChild(countdownDiv);
        }
    });
</script>
@endpush