@extends('layouts.admin')

@section('title', 'View News')

@push('styles')
<style>
    /* Custom styles for HTML content display */
    .news-content-display {
        line-height: 1.7;
    }
    .news-content-display h1 {
        font-size: 1.875rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: #1f2937;
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 0.5rem;
    }
    .news-content-display h2 {
        font-size: 1.5rem;
        font-weight: 600;
        margin: 1.5rem 0 0.75rem 0;
        color: #1f2937;
    }
    .news-content-display h3 {
        font-size: 1.25rem;
        font-weight: 600;
        margin: 1.25rem 0 0.5rem 0;
        color: #374151;
    }
    .news-content-display h4 {
        font-size: 1.125rem;
        font-weight: 600;
        margin: 1rem 0 0.5rem 0;
        color: #374151;
    }
    .news-content-display p {
        margin-bottom: 1rem;
        color: #374151;
    }
    .news-content-display ul, .news-content-display ol {
        margin: 1rem 0;
        padding-left: 1.5rem;
    }
    .news-content-display li {
        margin-bottom: 0.5rem;
        color: #374151;
    }
    .news-content-display ul li {
        list-style-type: disc;
    }
    .news-content-display ol li {
        list-style-type: decimal;
    }
    .news-content-display blockquote {
        border-left: 4px solid #3b82f6;
        padding-left: 1rem;
        margin: 1.5rem 0;
        font-style: italic;
        color: #6b7280;
        background-color: #f8fafc;
        padding: 1rem;
        border-radius: 0.375rem;
    }
    .news-content-display table {
        width: 100%;
        border-collapse: collapse;
        margin: 1.5rem 0;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        overflow: hidden;
    }
    .news-content-display th, .news-content-display td {
        border: 1px solid #e5e7eb;
        padding: 0.75rem;
        text-align: left;
    }
    .news-content-display th {
        background-color: #f9fafb;
        font-weight: 600;
        color: #374151;
    }
    .news-content-display td {
        color: #374151;
    }
    .news-content-display a {
        color: #3b82f6;
        text-decoration: underline;
        font-weight: 500;
    }
    .news-content-display a:hover {
        color: #1d4ed8;
    }
    .news-content-display strong {
        font-weight: 600;
        color: #1f2937;
    }
    .news-content-display em {
        font-style: italic;
        color: #6b7280;
    }
    .news-content-display code {
        background-color: #f3f4f6;
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
        font-family: 'Courier New', monospace;
        font-size: 0.875rem;
        color: #dc2626;
    }
</style>
@endpush

@php
    $pageTitle = 'View News';
    $pageDescription = $news->title;
    $breadcrumbs = [
        ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
        ['title' => 'News', 'url' => route('admin.news.index')],
        ['title' => 'View', 'url' => '#']
    ];
@endphp

@section('page-header')
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-semibold text-gray-900">{{ $pageTitle }}</h1>
            <p class="mt-2 text-gray-600">{{ $pageDescription }}</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('admin.news.edit', $news) }}" class="material-button material-button-primary flex items-center">
                <i class="material-icons mr-2">edit</i>
                Edit News
            </a>
            <a href="{{ route('admin.news.index') }}" class="material-button material-button-secondary flex items-center">
                <i class="material-icons mr-2">arrow_back</i>
                Back to News
            </a>
        </div>
    </div>
@endsection

@section('content')

<div class="space-y-6">
    <!-- Basic Information -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="material-icons mr-2 text-blue-600">article</i>
                Basic Information
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                    <p class="text-gray-900 font-medium">{{ $news->title }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Slug</label>
                    <p class="text-gray-900 font-mono text-sm bg-gray-50 px-3 py-2 rounded-md">{{ $news->slug }}</p>
                </div>
                <div class="lg:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <div class="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                        <div class="news-content-display">
                            {!! $news->description !!}
                        </div>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $news->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                        <i class="material-icons mr-1 text-xs">{{ $news->status === 'active' ? 'check_circle' : 'cancel' }}</i>
                        {{ ucfirst($news->status) }}
                    </span>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Featured</label>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $news->is_featured ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800' }}">
                        <i class="material-icons mr-1 text-xs">{{ $news->is_featured ? 'star' : 'star_border' }}</i>
                        {{ $news->is_featured ? 'Featured' : 'Regular' }}
                    </span>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
                    <p class="text-gray-900">{{ $news->sort_order }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Published At</label>
                    <p class="text-gray-900">{{ $news->published_at ? $news->published_at->format('M d, Y H:i') : 'Not published' }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Created At</label>
                    <p class="text-gray-900">{{ $news->created_at->format('M d, Y H:i') }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Updated At</label>
                    <p class="text-gray-900">{{ $news->updated_at->format('M d, Y H:i') }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Images -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="material-icons mr-2 text-blue-600">image</i>
                Images
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Main Image -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Main Image</label>
                    @if($news->main_image)
                        <div class="relative group">
                            <img src="{{ $news->main_image_url }}" alt="Main Image"
                                 class="w-full h-48 object-cover rounded-lg border-2 border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 rounded-lg transition-all duration-200"></div>
                        </div>
                    @else
                        <div class="w-full h-48 bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                            <div class="text-center">
                                <i class="material-icons text-gray-400 text-3xl mb-2">image</i>
                                <span class="text-gray-500 text-sm">No main image</span>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Sub Image -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Sub Image</label>
                    @if($news->sub_image)
                        <div class="relative group">
                            <img src="{{ $news->sub_image_url }}" alt="Sub Image"
                                 class="w-full h-48 object-cover rounded-lg border-2 border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 rounded-lg transition-all duration-200"></div>
                        </div>
                    @else
                        <div class="w-full h-48 bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                            <div class="text-center">
                                <i class="material-icons text-gray-400 text-3xl mb-2">image</i>
                                <span class="text-gray-500 text-sm">No sub image</span>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- OG Image -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">OG Image</label>
                    @if($news->og_image)
                        <div class="relative group">
                            <img src="{{ $news->og_image_url }}" alt="OG Image"
                                 class="w-full h-48 object-cover rounded-lg border-2 border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 rounded-lg transition-all duration-200"></div>
                        </div>
                    @else
                        <div class="w-full h-48 bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                            <div class="text-center">
                                <i class="material-icons text-gray-400 text-3xl mb-2">share</i>
                                <span class="text-gray-500 text-sm">No OG image</span>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Categories and Tags -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Categories -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="material-icons mr-2 text-blue-600">category</i>
                    Categories ({{ $news->categories->count() }})
                </h3>
            </div>
            <div class="p-6">
                @if($news->categories->count() > 0)
                    <div class="space-y-3">
                        @foreach($news->categories as $category)
                            <div class="flex items-center p-4 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors duration-200">
                                <div class="w-5 h-5 rounded-full mr-3 border-2 border-white shadow-sm" style="background-color: {{ $category->color }}"></div>
                                <div class="flex-1">
                                    <div class="font-medium text-gray-900">{{ $category->name }}</div>
                                    @if($category->description)
                                        <div class="text-sm text-gray-600 mt-1">{{ Str::limit($category->description, 80) }}</div>
                                    @endif
                                </div>
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium {{ $category->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    <i class="material-icons mr-1 text-xs">{{ $category->status === 'active' ? 'check_circle' : 'cancel' }}</i>
                                    {{ ucfirst($category->status) }}
                                </span>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="material-icons text-gray-400 text-4xl mb-3">category</i>
                        <p class="text-gray-500">No categories assigned</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Tags -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="material-icons mr-2 text-blue-600">local_offer</i>
                    Tags ({{ $news->tags->count() }})
                </h3>
            </div>
            <div class="p-6">
                @if($news->tags->count() > 0)
                    <div class="space-y-3">
                        @foreach($news->tags as $tag)
                            <div class="flex items-center p-4 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors duration-200">
                                <div class="w-5 h-5 rounded-full mr-3 border-2 border-white shadow-sm" style="background-color: {{ $tag->color }}"></div>
                                <div class="flex-1">
                                    <div class="font-medium text-gray-900">{{ $tag->name }}</div>
                                    @if($tag->description)
                                        <div class="text-sm text-gray-600 mt-1">{{ Str::limit($tag->description, 80) }}</div>
                                    @endif
                                </div>
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium {{ $tag->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    <i class="material-icons mr-1 text-xs">{{ $tag->status === 'active' ? 'check_circle' : 'cancel' }}</i>
                                    {{ ucfirst($tag->status) }}
                                </span>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="material-icons text-gray-400 text-4xl mb-3">local_offer</i>
                        <p class="text-gray-500">No tags assigned</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- SEO Meta Information -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="material-icons mr-2 text-blue-600">search</i>
                SEO Meta Information
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Meta Title</label>
                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ $news->meta_title ?: 'Not set' }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Meta Description</label>
                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ $news->meta_description ?: 'Not set' }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Meta Keywords</label>
                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ $news->meta_keywords ?: 'Not set' }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Canonical URL</label>
                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md break-all">{{ $news->canonical_url ?: 'Not set' }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Robots Meta</label>
                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ $news->robots_meta }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Open Graph Information -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="material-icons mr-2 text-blue-600">share</i>
                Open Graph Information
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">OG Title</label>
                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ $news->og_title ?: 'Not set' }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">OG Description</label>
                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ $news->og_description ?: 'Not set' }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">OG Type</label>
                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ $news->og_type }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection