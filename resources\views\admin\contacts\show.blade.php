@extends('layouts.admin')

@section('title', 'Contact Message Details')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Contact Message Details</h1>
            <p class="text-gray-600 dark:text-gray-400">Submitted on {{ $contact->created_at->format('F j, Y \a\t g:i A') }}</p>
        </div>
        <div class="flex space-x-2">
            <a href="{{ route('admin.contacts.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md">
                ← Back to List
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Contact Details -->
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Message Details</h2>
                
                <!-- Contact Info -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Name</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $contact->name }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">
                            <a href="mailto:{{ $contact->email }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400">
                                {{ $contact->email }}
                            </a>
                        </p>
                    </div>
                    @if($contact->phone)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Phone</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">
                            <a href="tel:{{ $contact->phone }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400">
                                {{ $contact->phone }}
                            </a>
                        </p>
                    </div>
                    @endif
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Subject</label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $contact->subject_display }}</p>
                    </div>
                </div>

                <!-- Message -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Message</label>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <p class="text-sm text-gray-900 dark:text-white whitespace-pre-wrap">{{ $contact->message }}</p>
                    </div>
                </div>

                <!-- Quick Reply -->
                <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                    <h3 class="text-md font-semibold text-gray-900 dark:text-white mb-4">Quick Reply</h3>
                    <div class="flex space-x-2">
                        <a href="mailto:{{ $contact->email }}?subject=Re: {{ $contact->subject_display }}&body=Hi {{ $contact->name }},%0D%0A%0D%0AThank you for contacting us.%0D%0A%0D%0ABest regards,%0D%0A{{ config('app.name') }} Team" 
                           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm">
                            Reply via Email
                        </a>
                        @if($contact->phone)
                        <a href="tel:{{ $contact->phone }}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm">
                            Call
                        </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Status & Actions -->
        <div class="lg:col-span-1">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Status & Actions</h2>
                
                <!-- Current Status -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Current Status</label>
                    <span class="px-3 py-1 text-sm font-semibold rounded-full {{ $contact->status_badge_color }}">
                        {{ ucfirst(str_replace('_', ' ', $contact->status)) }}
                    </span>
                </div>

                <!-- Update Status -->
                <form method="POST" action="{{ route('admin.contacts.update-status', $contact) }}" class="mb-6">
                    @csrf
                    @method('PATCH')
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Update Status</label>
                    <div class="flex space-x-2">
                        <select name="status" class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                            <option value="new" {{ $contact->status === 'new' ? 'selected' : '' }}>New</option>
                            <option value="in_progress" {{ $contact->status === 'in_progress' ? 'selected' : '' }}>In Progress</option>
                            <option value="responded" {{ $contact->status === 'responded' ? 'selected' : '' }}>Responded</option>
                            <option value="closed" {{ $contact->status === 'closed' ? 'selected' : '' }}>Closed</option>
                        </select>
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm">
                            Update
                        </button>
                    </div>
                </form>

                <!-- Timestamps -->
                <div class="space-y-3 text-sm">
                    <div>
                        <span class="font-medium text-gray-700 dark:text-gray-300">Submitted:</span>
                        <span class="text-gray-900 dark:text-white">{{ $contact->created_at->format('M j, Y g:i A') }}</span>
                    </div>
                    @if($contact->responded_at)
                    <div>
                        <span class="font-medium text-gray-700 dark:text-gray-300">Responded:</span>
                        <span class="text-gray-900 dark:text-white">{{ $contact->responded_at->format('M j, Y g:i A') }}</span>
                    </div>
                    @endif
                    @if($contact->updated_at && $contact->updated_at != $contact->created_at)
                    <div>
                        <span class="font-medium text-gray-700 dark:text-gray-300">Last Updated:</span>
                        <span class="text-gray-900 dark:text-white">{{ $contact->updated_at->format('M j, Y g:i A') }}</span>
                    </div>
                    @endif
                </div>

                <!-- Delete -->
                <div class="border-t border-gray-200 dark:border-gray-700 pt-6 mt-6">
                    <form method="POST" action="{{ route('admin.contacts.destroy', $contact) }}">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm"
                                onclick="return confirm('Are you sure you want to delete this contact message? This action cannot be undone.')">
                            Delete Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
