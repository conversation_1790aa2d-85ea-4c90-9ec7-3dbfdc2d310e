<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Notifications\CustomResetPasswordNotification;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'profile_image',
        'status',
        'locked_until',
        'failed_login_attempts',
        'last_login_at',
        'last_login_ip',
        'is_deactivated',
        'deactivation_reason',
        'deactivated_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'locked_until' => 'datetime',
        'last_login_at' => 'datetime',
        'deactivated_at' => 'datetime',
        'is_deactivated' => 'boolean',
    ];

    /**
     * The roles that belong to the user.
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class);
    }
    /**
     * Check if the user has the given role.
     *
     * @param string|Role $role
     * @return bool
     */
    public function hasRole($role): bool
    {
        if (is_string($role)) {
            return $this->roles->where('slug', $role)->count() > 0;
        }
        
        return !!$role->intersect($this->roles)->count();
    }

    /**
     * Check if user has a specific permission
     */
    public function hasPermission($permission): bool
    {
        // For debugging
        
        $permissions = $this->getAllPermissions()->pluck('slug')->toArray();
        
        $result = in_array($permission, $permissions);
        
        return $result;
    }
    
    /**
     * Get all permissions for the user through roles
     */
    public function getAllPermissions()
    {
        // Get permissions from roles only
        return $this->roles->flatMap(function ($role) {
            return $role->permissions;
        })->unique('id');
    }

    /**
     * Get the profile image URL
     */
    public function getProfileImageUrlAttribute()
    {
        if ($this->profile_image) {
            return asset('storage/' . $this->profile_image);
        }

        return "data:image/svg+xml,%3csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100' height='100' fill='%23f3f4f6'/%3e%3ctext x='50%25' y='50%25' font-size='14' text-anchor='middle' dy='.3em' fill='%236b7280'%3eNo Image%3c/text%3e%3c/svg%3e";
    }

    /**
     * Send the password reset notification.
     *
     * @param  string  $token
     * @return void
     */
    public function sendPasswordResetNotification($token)
    {
        $this->notify(new CustomResetPasswordNotification($token));
    }

    /**
     * Get user login logs
     */
    public function loginLogs()
    {
        return $this->hasMany(UserLoginLog::class);
    }

    /**
     * Check if user account is currently locked
     */
    public function isLocked(): bool
    {
        return $this->locked_until && $this->locked_until->isFuture();
    }

    /**
     * Check if user account is deactivated
     */
    public function isDeactivated(): bool
    {
        return $this->is_deactivated;
    }

    /**
     * Check if user can login
     */
    public function canLogin(): bool
    {
        return !$this->isDeactivated() && !$this->isLocked() && $this->status === 'active';
    }

    /**
     * Get remaining lockout time in minutes
     */
    public function getRemainingLockoutMinutes(): int
    {
        if (!$this->isLocked()) {
            return 0;
        }

        // Calculate difference correctly - locked_until is in the future
        $minutes = now()->diffInMinutes($this->locked_until, false);
        return max(0, (int) $minutes);
    }

    /**
     * Lock user account
     */
    public function lockAccount(int $minutes): void
    {
        $this->locked_until = now()->addMinutes($minutes);
        $this->save();
    }

    /**
     * Unlock user account
     */
    public function unlockAccount(): void
    {
        $this->locked_until = null;
        $this->failed_login_attempts = 0;
        $this->save();
    }

    /**
     * Deactivate user account
     */
    public function deactivateAccount(string $reason = 'Multiple failed login attempts'): void
    {
        $this->is_deactivated = true;
        $this->deactivation_reason = $reason;
        $this->deactivated_at = now();
        $this->locked_until = null;
        $this->save();
    }

    /**
     * Reactivate user account
     */
    public function reactivateAccount(): void
    {
        $this->is_deactivated = false;
        $this->deactivation_reason = null;
        $this->deactivated_at = null;
        $this->locked_until = null;
        $this->failed_login_attempts = 0;
        $this->save();
    }

    /**
     * Record successful login
     */
    public function recordSuccessfulLogin(string $ip, ?string $userAgent = null): void
    {
        $this->last_login_at = now();
        $this->last_login_ip = $ip;
        $this->failed_login_attempts = 0;
        $this->locked_until = null;
        $this->save();

        // Log the successful login
        UserLoginLog::logAttempt(
            $this,
            $this->email,
            'success',
            $ip,
            $userAgent,
            session()->getId()
        );
    }

    /**
     * Record failed login attempt
     */
    public function recordFailedLogin(string $reason = 'Invalid credentials'): void
    {
        $this->increment('failed_login_attempts');
        $this->save();

        // Log the failed attempt
        UserLoginLog::logAttempt(
            $this,
            $this->email,
            'failed',
            request()->ip(),
            request()->userAgent(),
            null,
            $reason
        );
    }

    /**
     * Get account status badge
     */
    public function getStatusBadgeAttribute(): string
    {
        if ($this->isDeactivated()) {
            return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Deactivated</span>';
        }

        if ($this->isLocked()) {
            return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Locked</span>';
        }

        if ($this->status === 'active') {
            return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>';
        }

        return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Inactive</span>';
    }
}



