@extends('layouts.admin')

@section('title', 'Edit Tag')

@php
    $pageTitle = 'Edit Tag';
    $pageDescription = 'Update tag information';
    $breadcrumbs = [
        ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
        ['title' => 'Tags', 'url' => route('admin.tags.index')],
        ['title' => 'Edit', 'url' => '#']
    ];
@endphp

@section('page-header')
<div class="flex items-center justify-between">
    <div>
        <h1 class="text-3xl font-semibold text-gray-900">{{ $pageTitle }}</h1>
        <p class="mt-2 text-gray-600">{{ $pageDescription }}</p>
    </div>
    <div class="flex space-x-3">
        <a href="{{ route('admin.tags.show', $tag) }}" class="material-button material-button-md material-button-secondary flex items-center">
            <i class="material-icons mr-2">visibility</i>
            View Tag
        </a>
        <a href="{{ route('admin.tags.index') }}" class="material-button material-button-md material-button-secondary flex items-center">
            <i class="material-icons mr-2">arrow_back</i>
            Back to Tags
        </a>
    </div>
</div>
@endsection

@section('content')

<!-- Edit Tag Form -->
<div class="material-card">
    <form method="POST" action="{{ route('admin.tags.update', $tag) }}" class="p-6 space-y-6" id="tag_edit_form" enctype="multipart/form-data">
        @csrf
        @method('PUT')

        <!-- Form Layout - Responsive Grid -->
        <div class="space-y-6">
            <!-- Row 1: Name and Slug -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Name Field -->
                <div class="order-1 lg:order-1">
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Tag Name <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">local_offer</i>
                        </div>
                        <input id="name"
                            name="name"
                            type="text"
                            required
                            value="{{ old('name', $tag->name) }}"
                            class="material-input-with-icon @error('name') material-input-error @enderror"
                            placeholder="Enter tag name">
                    </div>
                    @error('name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Slug Field -->
                <div class="order-2 lg:order-2">
                    <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">
                        Slug <small class="text-gray-500">(auto-generated)</small>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">link</i>
                        </div>
                        <input id="slug"
                            name="slug"
                            type="text"
                            value="{{ old('slug', $tag->slug) }}"
                            class="material-input-with-icon @error('slug') material-input-error @enderror"
                            placeholder="Auto-generated from name">
                    </div>
                    @error('slug')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Row 2: Color and Status -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Color Field -->
                <div class="order-3 lg:order-1">
                    <label for="color" class="block text-sm font-medium text-gray-700 mb-2">
                        Color <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">palette</i>
                        </div>
                        <div class="flex items-center space-x-3">
                            <input type="color"
                                   name="color"
                                   id="color"
                                   value="{{ old('color', $tag->color) }}"
                                   class="w-12 h-10 border border-gray-300 rounded-md cursor-pointer @error('color') border-red-500 @enderror">
                            <input type="text"
                                   id="color_text"
                                   value="{{ old('color', $tag->color) }}"
                                   class="material-input flex-1 @error('color') border-red-500 @enderror"
                                   placeholder="#3B82F6"
                                   readonly>
                        </div>
                    </div>
                    @error('color')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Status Field -->
                <div class="order-4 lg:order-2">
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                        Status <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">toggle_on</i>
                        </div>
                        <select id="status"
                            name="status"
                            required
                            class="material-select-with-icon @error('status') material-select-error @enderror">
                            <option value="active" {{ old('status', $tag->status) == 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ old('status', $tag->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                    </div>
                    @error('status')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Row 3: Description and Sort Order -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Description Field -->
                <div class="order-5 lg:order-1">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        Description
                    </label>
                    <div class="relative">
                        <div class="absolute top-3 left-0 pl-3 flex items-start pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">description</i>
                        </div>
                        <textarea id="description"
                            name="description"
                            rows="4"
                            class="material-input-with-icon @error('description') material-input-error @enderror"
                            placeholder="Enter tag description">{{ old('description', $tag->description) }}</textarea>
                    </div>
                    @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Sort Order Field -->
                <div class="order-6 lg:order-2">
                    <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">
                        Sort Order
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">sort</i>
                        </div>
                        <input id="sort_order"
                            name="sort_order"
                            type="number"
                            min="0"
                            value="{{ old('sort_order', $tag->sort_order) }}"
                            class="material-input-with-icon @error('sort_order') material-input-error @enderror"
                            placeholder="0">
                    </div>
                    @error('sort_order')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-xs text-gray-500">Lower numbers appear first</p>
                </div>
            </div>
        </div>

            <!-- SEO Information Section -->
            <div class="space-y-6 mt-8">
                <div class="border-b border-gray-200 pb-4">
                    <h3 class="text-lg font-medium text-gray-900">SEO Information</h3>
                    <p class="mt-1 text-sm text-gray-600">Search engine optimization settings</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Meta Title -->
                    <div>
                        <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-2">
                            Meta Title
                        </label>
                        <input type="text"
                               name="meta_title"
                               id="meta_title"
                               value="{{ old('meta_title', $tag->meta_title) }}"
                               class="material-input @error('meta_title') border-red-500 @enderror"
                               placeholder="SEO title for search engines">
                        @error('meta_title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Canonical URL -->
                    <div>
                        <label for="canonical_url" class="block text-sm font-medium text-gray-700 mb-2">
                            Canonical URL
                        </label>
                        <input type="url"
                               name="canonical_url"
                               id="canonical_url"
                               value="{{ old('canonical_url', $tag->canonical_url) }}"
                               class="material-input @error('canonical_url') border-red-500 @enderror"
                               placeholder="https://example.com/tags/tag-name">
                        @error('canonical_url')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Meta Description -->
                <div>
                    <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">
                        Meta Description
                    </label>
                    <textarea name="meta_description"
                              id="meta_description"
                              rows="3"
                              class="material-input @error('meta_description') border-red-500 @enderror"
                              placeholder="Brief description for search engines (150-160 characters)">{{ old('meta_description', $tag->meta_description) }}</textarea>
                    @error('meta_description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Meta Keywords -->
                <div>
                    <label for="meta_keywords" class="block text-sm font-medium text-gray-700 mb-2">
                        Meta Keywords
                    </label>
                    <input type="text"
                           name="meta_keywords"
                           id="meta_keywords"
                           value="{{ old('meta_keywords', $tag->meta_keywords) }}"
                           class="material-input @error('meta_keywords') border-red-500 @enderror"
                           placeholder="keyword1, keyword2, keyword3">
                    @error('meta_keywords')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-xs text-gray-500">Separate keywords with commas</p>
                </div>

                <!-- Robots Meta -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="robots_meta" class="block text-sm font-medium text-gray-700 mb-2">
                            Robots Meta
                        </label>
                        <select name="robots_meta"
                                id="robots_meta"
                                class="material-input @error('robots_meta') border-red-500 @enderror">
                            <option value="index,follow" {{ old('robots_meta', $tag->robots_meta) == 'index,follow' ? 'selected' : '' }}>Index, Follow</option>
                            <option value="index,nofollow" {{ old('robots_meta', $tag->robots_meta) == 'index,nofollow' ? 'selected' : '' }}>Index, No Follow</option>
                            <option value="noindex,follow" {{ old('robots_meta', $tag->robots_meta) == 'noindex,follow' ? 'selected' : '' }}>No Index, Follow</option>
                            <option value="noindex,nofollow" {{ old('robots_meta', $tag->robots_meta) == 'noindex,nofollow' ? 'selected' : '' }}>No Index, No Follow</option>
                        </select>
                        @error('robots_meta')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Open Graph Information Section -->
            <div class="space-y-6 mt-8">
                <div class="border-b border-gray-200 pb-4">
                    <h3 class="text-lg font-medium text-gray-900">Open Graph Information</h3>
                    <p class="mt-1 text-sm text-gray-600">Social media sharing settings</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- OG Title -->
                    <div>
                        <label for="og_title" class="block text-sm font-medium text-gray-700 mb-2">
                            OG Title
                        </label>
                        <input type="text"
                               name="og_title"
                               id="og_title"
                               value="{{ old('og_title', $tag->og_title) }}"
                               class="material-input @error('og_title') border-red-500 @enderror"
                               placeholder="Title for social media sharing">
                        @error('og_title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- OG Type -->
                    <div>
                        <label for="og_type" class="block text-sm font-medium text-gray-700 mb-2">
                            OG Type
                        </label>
                        <select name="og_type"
                                id="og_type"
                                class="material-input @error('og_type') border-red-500 @enderror">
                            <option value="website" {{ old('og_type', $tag->og_type) == 'website' ? 'selected' : '' }}>Website</option>
                            <option value="article" {{ old('og_type', $tag->og_type) == 'article' ? 'selected' : '' }}>Article</option>
                            <option value="product" {{ old('og_type', $tag->og_type) == 'product' ? 'selected' : '' }}>Product</option>
                        </select>
                        @error('og_type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- OG Description -->
                <div>
                    <label for="og_description" class="block text-sm font-medium text-gray-700 mb-2">
                        OG Description
                    </label>
                    <textarea name="og_description"
                              id="og_description"
                              rows="3"
                              class="material-input @error('og_description') border-red-500 @enderror"
                              placeholder="Description for social media sharing">{{ old('og_description', $tag->og_description) }}</textarea>
                    @error('og_description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- OG Image -->
                <div class="grid grid-cols-1 gap-6">
                    <div>
                        <label for="og_image" class="block text-sm font-medium text-gray-700 mb-2">
                            OG Image <small class="text-gray-500">(recommended: 1200x630px)</small>
                        </label>
                        <div class="mt-1 flex items-center space-x-4">
                            <!-- Image Preview -->
                            <div class="flex-shrink-0">
                                <img id="og-image-preview"
                                    class="h-20 w-32 rounded-lg object-cover border-2 border-gray-300 bg-gray-100"
                                    src="{{ $tag->og_image_url }}"
                                    alt="OG image preview">
                            </div>
                            <!-- File Input and Remove -->
                            <div class="flex-1">
                                <div class="flex items-center space-x-2">
                                    <div class="relative">
                                        <input id="og_image" name="og_image" type="file" accept="image/*"
                                            class="hidden" onchange="previewOgImage(this)">
                                        <label for="og_image"
                                            class="material-button material-button-sm material-button-secondary cursor-pointer flex items-center">
                                            <i class="material-icons mr-2 text-sm">upload</i>
                                            Choose New Image
                                        </label>
                                    </div>
                                    @if($tag->og_image)
                                        <div class="flex items-center">
                                            <input id="remove_og_image" name="remove_og_image" type="checkbox" value="1"
                                                class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded">
                                            <label for="remove_og_image" class="ml-2 block text-sm text-red-600">
                                                Remove current image
                                            </label>
                                        </div>
                                    @endif
                                </div>
                                <p class="mt-1 text-xs text-gray-500">PNG, JPG, GIF up to 2MB. Recommended: 1200x630px</p>
                            </div>
                        </div>
                        @error('og_image')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            <a href="{{ route('admin.tags.index') }}" class="material-button material-button-sm material-button-secondary flex items-center">
                Cancel
            </a>
            <button type="submit" class="material-button material-button-sm material-button-primary flex items-center">
                <i class="material-icons mr-2 text-sm">save</i>
                Update Tag
            </button>
        </div>
    </form>
</div>
@endsection

@push('scripts')
    @vite('resources/js/utils/common.js')
    <script src="https://code.jquery.com/jquery-3.7.0.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
    <script>
        $(document).ready(function() {
            // Color picker sync
            $('#color').on('input', function() {
                $('#color_text').val($(this).val());
            });

            $('#color_text').on('input', function() {
                let color = $(this).val();
                if (/^#[0-9A-F]{6}$/i.test(color)) {
                    $('#color').val(color);
                }
            });

            // Auto-generate slug from name
            function generateSlug(text) {
                return text
                    .toLowerCase()
                    .trim()
                    .replace(/[^\w\s-]/g, '') // Remove special characters
                    .replace(/[\s_-]+/g, '-') // Replace spaces, underscores, and multiple hyphens with single hyphen
                    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
            }

            let manualSlugEdit = $('#slug').val().trim().length > 0;

            $('#name').on('input keyup paste', function() {
                if (!manualSlugEdit || $('#slug').val().trim() === '') {
                    let slug = generateSlug($(this).val());
                    $('#slug').val(slug);
                }
            });

            // Track manual slug editing
            $('#slug').on('input', function() {
                manualSlugEdit = true;
            });

            // Reset manual edit flag when slug is cleared
            $('#slug').on('blur', function() {
                if ($(this).val().trim() === '') {
                    manualSlugEdit = false;
                }
            });

            // jQuery Form validation
            $("#tag_edit_form").validate({
                rules: {
                    name: {
                        required: true,
                        minlength: 2,
                        maxlength: 255
                    },
                    slug: {
                        maxlength: 255
                    },
                    color: {
                        required: true,
                        pattern: /^#[0-9A-Fa-f]{6}$/
                    },
                    status: {
                        required: true
                    },
                    sort_order: {
                        min: 0
                    },
                    og_image: {
                        extension: "jpg|jpeg|png|gif",
                        filesize: 2097152 // 2MB in bytes
                    }
                },
                messages: {
                    name: {
                        required: "Please enter a tag name",
                        minlength: "Tag name must be at least 2 characters",
                        maxlength: "Tag name cannot exceed 255 characters"
                    },
                    color: {
                        required: "Please select a color",
                        pattern: "Please enter a valid hex color code"
                    },
                    status: {
                        required: "Please select a status"
                    },
                    sort_order: {
                        min: "Sort order must be 0 or greater"
                    },
                    og_image: {
                        extension: "Please select a valid image file (JPG, JPEG, PNG, GIF)",
                        filesize: "Image file size must be less than 2MB"
                    }
                },
                errorElement: 'span',
                errorPlacement: function (error, element) {
                    error.addClass("text-red-500 text-xs mt-1").insertAfter(element.closest('.relative'));
                },
                highlight: function (element) {
                    $(element).addClass('border-red-500').removeClass('border-gray-300');
                },
                unhighlight: function (element) {
                    $(element).removeClass('border-red-500').addClass('border-gray-300');
                }
            });

            // Custom validation methods
            $.validator.addMethod("filesize", function(value, element, param) {
                return this.optional(element) || (element.files[0] && element.files[0].size <= param);
            }, "File size must be less than {0} bytes");

            // Preview OG image
            window.previewOgImage = function(input) {
                if (input.files && input.files[0]) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        document.getElementById('og-image-preview').src = e.target.result;
                        // Uncheck remove image checkbox if new image is selected
                        const removeCheckbox = document.getElementById('remove_og_image');
                        if (removeCheckbox) {
                            removeCheckbox.checked = false;
                        }
                    };
                    reader.readAsDataURL(input.files[0]);
                }
            };
        });
    </script>
@endpush
