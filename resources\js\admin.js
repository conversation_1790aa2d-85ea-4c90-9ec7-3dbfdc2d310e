/**
 * Admin-specific JavaScript
 * Forces light mode for admin panel regardless of user preferences
 */

document.addEventListener('DOMContentLoaded', function() {
    // Force light mode for admin panel
    const html = document.documentElement;
    
    // Remove dark class if it exists
    html.classList.remove('dark');
    
    // Ensure light class is applied
    html.classList.add('light');
    
    // Override localStorage theme setting for this session only
    const originalTheme = localStorage.getItem('theme');
    
    // Create a proxy for localStorage to intercept theme changes
    const localStorageProxy = {
        getItem: function(key) {
            if (key === 'theme') {
                return 'light'; // Always return light for theme in admin
            }
            return localStorage.getItem(key);
        },
        setItem: function(key, value) {
            if (key === 'theme') {
                // Don't actually change the theme in localStorage
                // This prevents the admin panel from affecting the frontend theme
                return;
            }
            localStorage.setItem(key, value);
        },
        removeItem: function(key) {
            localStorage.removeItem(key);
        }
    };
    
    // Override localStorage for this page
    Object.defineProperty(window, 'localStorage', {
        get: function() {
            return localStorageProxy;
        }
    });
});