@extends('layouts.frontend')

@section('title', $tag->name)

@php
    // Function to generate random color classes for categories with dark mode support
    function getRandomCategoryColor() {
        $colors = [
            ['text' => 'text-red-700 dark:text-red-400', 'hover' => 'hover:text-red-600 dark:hover:text-red-300'],
            ['text' => 'text-blue-700 dark:text-blue-400', 'hover' => 'hover:text-blue-600 dark:hover:text-blue-300'],
            ['text' => 'text-green-700 dark:text-green-400', 'hover' => 'hover:text-green-600 dark:hover:text-green-300'],
            ['text' => 'text-purple-700 dark:text-purple-400', 'hover' => 'hover:text-purple-600 dark:hover:text-purple-300'],
            ['text' => 'text-indigo-700 dark:text-indigo-400', 'hover' => 'hover:text-indigo-600 dark:hover:text-indigo-300']
        ];
        return $colors[array_rand($colors)];
    }
@endphp

@section('content')
<section class="bg-gray-50 dark:bg-gray-900 py-8 container mx-auto transition-colors duration-200">
    <div class="px-4 sm:px-6 lg:px-8">
        <!-- Tag Header -->
        <h1 class="relative border-b border-gray-300/70 dark:border-gray-600/70 pb-2.5 text-2xl font-medium text-gray-900 dark:text-white mb-6 before:absolute before:-bottom-px before:left-0 before:h-px before:w-24 before:bg-red-600 dark:before:bg-red-500 before:content-[''] transition-colors duration-200">
            Tag: {{ $tag->name }}
        </h1>
        
        <!-- Articles Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-5">
            @forelse($news as $item)
                <!-- Article -->
                <article class="py-0 flex">
                    <a class="article-image w-2/5 flex-shrink-0" href="{{ route('news.show', $item->slug) }}">
                        <div class="group aspect-h-9 aspect-w-16 overflow-hidden rounded bg-gray-100 dark:bg-gray-800 relative">
                            <img alt="{{ $item->title }}"
                                loading="lazy"
                                decoding="async"
                                class="rounded object-cover object-center transition duration-300 ease-in-out group-hover:scale-110"
                                style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent"
                                src="{{ asset('storage/' . $item->main_image) }}"
                                onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <!-- Image placeholder -->
                            <div class="absolute inset-0 bg-gray-200 rounded flex items-center justify-center" style="display:none;">
                                <svg class="h-8 w-8 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                            </div>
                        </div>
                    </a>
                    <div class="ml-3 flex-1">
                        @php $categoryColor = getRandomCategoryColor(); @endphp
                        @if($item->categories->isNotEmpty())
                            <a class="text-xs font-medium uppercase tracking-widest {{ $categoryColor['text'] }} transition-colors duration-300 ease-in-out {{ $categoryColor['hover'] }}" 
                               href="{{ route('category.show', $item->categories->first()->slug) }}">
                                {{ $item->categories->first()->name }}
                            </a>
                        @endif
                        <a href="{{ route('news.show', $item->slug) }}">
                            <h3 class="text-sm lg:text-sm font-medium leading-normal tracking-normal text-gray-900 dark:text-white decoration-gray-800 decoration-2 transition duration-300 ease-in-out">
                                {{ $item->title }}
                            </h3>
                        </a>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            {{ $item->published_at->format('M d, Y') }}
                        </p>
                    </div>
                </article>
            @empty
                <div class="col-span-full text-center py-12">
                    <p class="text-gray-600 dark:text-gray-400">No news articles found with this tag.</p>
                </div>
            @endforelse
        </div>
        
        <!-- Pagination -->
        <div class="mt-8">
            {{ $news->links() }}
        </div>
    </div>
</section>
@endsection
