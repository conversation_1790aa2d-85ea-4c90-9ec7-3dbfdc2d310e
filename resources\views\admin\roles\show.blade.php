@extends('layouts.admin')

@section('title', 'View Role')

@php
    $pageTitle = 'Role Details';
    $pageDescription = 'View role information and permissions';
    $breadcrumbs = [
        ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
        ['title' => 'Roles', 'url' => route('admin.roles.index')],
        ['title' => 'View', 'url' => '#']
    ];
@endphp

@section('page-header')
<div class="flex items-center justify-between">
    <div>
        <h1 class="text-3xl font-semibold text-gray-900">{{ $pageTitle }}</h1>
        <p class="mt-2 text-gray-600">{{ $pageDescription }}</p>
    </div>
    <div class="flex space-x-3">
        <a href="{{ route('admin.roles.edit', $role) }}" class="material-button material-button-primary flex items-center">
            <i class="material-icons mr-2">edit</i>
            Edit Role
        </a>
        <a href="{{ route('admin.roles.index') }}" class="material-button material-button-secondary flex items-center">
            <i class="material-icons mr-2">arrow_back</i>
            Back to Roles
        </a>
    </div>
</div>
@endsection

@section('content')

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Role Profile Card -->
    <div class="lg:col-span-1">
        <div class="material-card p-6">
            <div class="text-center">
                <div class="w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-white font-bold text-2xl">{{ substr($role->name, 0, 1) }}</span>
                </div>
                <h3 class="text-xl font-semibold text-gray-900">{{ $role->name }}</h3>
                <p class="text-gray-600">{{ $role->slug }}</p>
                
                <!-- Status Badges -->
                <div class="flex justify-center space-x-2 mt-4">
                    <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                        <i class="material-icons mr-1 text-xs">group</i>
                        {{ $role->users->count() }} Users
                    </span>
                    <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                        <i class="material-icons mr-1 text-xs">verified</i>
                        {{ $role->permissions->count() }} Permissions
                    </span>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="mt-6 space-y-3">
                <a href="{{ route('admin.roles.edit', $role) }}"
                   class="w-full material-button material-button-sm material-button-secondary flex items-center justify-center">
                    <i class="material-icons mr-2 text-sm">edit</i>
                    Edit Role
                </a>

                @if($role->slug !== 'admin')
                    <form method="POST" action="{{ route('admin.roles.destroy', $role) }}"
                          onsubmit="return confirm('Are you sure you want to delete this role? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit"
                                class="w-full material-button material-button-sm material-button-danger flex items-center justify-center">
                            <i class="material-icons mr-2 text-sm">delete</i>
                            Delete Role
                        </button>
                    </form>
                @endif
            </div>
        </div>
    </div>

    <!-- Role Information -->
    <div class="lg:col-span-2 space-y-6">
        <!-- Basic Information -->
        <div class="material-card">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="material-icons mr-2">info</i>
                    Basic Information
                </h3>
            </div>
            <div class="p-6">
                <dl class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Role Name</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $role->name }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Slug</dt>
                        <dd class="mt-1 text-sm text-gray-900 font-mono">{{ $role->slug }}</dd>
                    </div>
                    <div class="sm:col-span-2">
                        <dt class="text-sm font-medium text-gray-500">Description</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {{ $role->description ?: 'No description provided' }}
                        </dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Role Activity -->
        <div class="material-card">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="material-icons mr-2">schedule</i>
                    Role Activity
                </h3>
            </div>
            <div class="p-6">
                <dl class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Created</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {{ $role->created_at->format('M d, Y \a\t g:i A') }}
                            <span class="text-gray-500">({{ $role->created_at->diffForHumans() }})</span>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {{ $role->updated_at->format('M d, Y \a\t g:i A') }}
                            <span class="text-gray-500">({{ $role->updated_at->diffForHumans() }})</span>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Role ID</dt>
                        <dd class="mt-1 text-sm text-gray-900 font-mono">#{{ $role->id }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Status</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                Active
                            </span>
                        </dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Permissions -->
        <div class="material-card">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="material-icons mr-2">verified_user</i>
                    Permissions ({{ $role->permissions->count() }})
                </h3>
            </div>
            <div class="p-6">
                @if($role->permissions->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        @foreach($role->permissions as $permission)
                            <div class="bg-gray-50 rounded-lg p-3 border border-gray-200">
                                <div class="font-medium text-gray-900">{{ $permission->name }}</div>
                                <div class="text-xs text-gray-500 mt-1">{{ $permission->slug }}</div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4 text-gray-500">
                        <i class="material-icons text-4xl mb-2">no_accounts</i>
                        <p>No permissions assigned to this role</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Users with this Role -->
        <div class="material-card">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="material-icons mr-2">people</i>
                    Users with this Role ({{ $role->users->count() }})
                </h3>
            </div>
            <div class="p-6">
                @if($role->users->count() > 0)
                    <div class="space-y-4">
                        @foreach($role->users as $user)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                                        <span class="text-white font-bold text-sm">{{ substr($user->name, 0, 1) }}</span>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-900">{{ $user->name }}</div>
                                        <div class="text-xs text-gray-500">{{ $user->email }}</div>
                                    </div>
                                </div>
                                <a href="{{ route('admin.users.show', $user) }}" class="text-blue-600 hover:text-blue-800">
                                    <i class="material-icons">visibility</i>
                                </a>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4 text-gray-500">
                        <i class="material-icons text-4xl mb-2">no_accounts</i>
                        <p>No users have been assigned this role</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@endsection

