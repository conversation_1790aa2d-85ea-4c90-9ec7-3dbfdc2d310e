/* DataTables Custom Styling */

/* Fix for horizontal scrollbar issue */
#users-table_wrapper {
    height: 100%;
    overflow-x: auto;
}

/* Ensure the table takes full width of its container */
#users-table {
    width: 100% !important;
}

/* Custom table styling */
/* DataTables Processing Indicator */
.dataTables_wrapper {
    position: relative;
}

.dataTables_wrapper .dataTables_processing {
    position: absolute;
    top: 50%;
    left: 50%;
    width: auto;
    height: auto;
    margin: 0;
    padding: 1rem 2rem;
    transform: translate(-50%, -50%);
    z-index: 1000;
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    color: #374151;
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Add a semi-transparent overlay behind the processing indicator */
.dataTables_wrapper.processing::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    z-index: 999;
}

/* Add a loading spinner to the processing indicator */
.dataTables_wrapper .dataTables_processing::before {
    content: '';
    display: inline-block;
    width: 1.5rem;
    height: 1.5rem;
    margin-right: 0.75rem;
    border-radius: 50%;
    border: 2px solid #e5e7eb;
    border-top-color: #3b82f6;
    animation: spinner 0.6s linear infinite;
}

/* Ensure the text is on the same line as the spinner */
.dataTables_wrapper .dataTables_processing > div {
    display: inline-flex;
    align-items: center;
    line-height: 1.5rem;
}

@keyframes spinner {
    to {
        transform: rotate(360deg);
    }
}

/* Ensure the overlay covers the entire wrapper */
.dataTables_wrapper.processing::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    z-index: 999;
}

/* Enhanced table row styling */
table.dataTable tbody tr {
    transition: all 0.2s ease;
}

table.dataTable tbody tr:hover {
    background-color: #f9fafb;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Action buttons styling */
.action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.action-btn.view {
    color: #3b82f6;
    background-color: #eff6ff;
}

.action-btn.view:hover {
    background-color: #dbeafe;
}

.action-btn.edit {
    color: #f59e0b;
    background-color: #fffbeb;
}

.action-btn.edit:hover {
    background-color: #fef3c7;
}

.action-btn.delete {
    color: #ef4444;
    background-color: #fee2e2;
}

.action-btn.delete:hover {
    background-color: #fecaca;
}

/* DataTables Pagination and Info Styling - Same Line */
.dataTables_paginate,
.dataTables_info {
    margin-top: 0;
}

/* DataTables Info Styling */
.dataTables_info {
    padding: 0.5rem 0;
    color: #64748b;
    font-size: 0.875rem;
    font-weight: 500;
}

/* DataTables Pagination Styling */
.dataTables_paginate {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.dataTables_paginate .paginate_button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 0 0.125rem;
    font-weight: 500;
    color: #374151;
    background-color: #ffffff;
    cursor: pointer;
    border: 1px solid #e5e7eb;
    border-radius: .375rem;
    padding: .375rem .95rem .375rem .75rem;
    font-size: .875rem;
    line-height: 1.25rem !important;
    transition: all .2s;
}

.dataTables_paginate .paginate_button:hover {
    border-color: #d1d5db;
    background-color: #f9fafb;
    color: #111827 !important;
}

.dataTables_paginate .paginate_button:focus {
    outline: none;
    border-color: #d1d5db;
}

.dataTables_paginate .paginate_button.current {
    background-color: #3b82f6;
    border-color: #3b82f6;
    color: white !important;
    font-weight: 600;
}

.dataTables_paginate .paginate_button.current:hover {
    background-color: #2563eb;
    border-color: #2563eb;
    color: white !important;
}

.dataTables_paginate .paginate_button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
    color: #9ca3af !important;
}

.dataTables_paginate .paginate_button.previous,
.dataTables_paginate .paginate_button.next {
    font-weight: 600;
}

/* For responsive layouts */
@media (max-width: 768px) {
    .dataTables_paginate {
        justify-content: center;
    }

    .dataTables_paginate .paginate_button {
        min-width: 2rem;
        padding: 0 0.5rem;
    }
}

/* Avatar styling */
.user-avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 5px;
}

/* Badge styling */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.verified {
    background-color: #d1fae5;
    color: #065f46;
}

.status-badge.unverified {
    background-color: #fef3c7;
    color: #92400e;
}

.role-badge {
    font-weight: 600;
    font-size: 0.875rem;
}

.role-badge.admin {
    color: #7c3aed;
}

.role-badge.user {
    color: #6b7280;
}

/* Search input styling */
#search-input {
    padding-left: 2.5rem;
}

#search-input::placeholder {
    color: #9ca3af;
    opacity: 1;
}

#search-input:focus {
    outline: none;
    border-color: #d1d5db;
}

/* Ensure the icon doesn't overlap with text */
.relative .absolute.inset-y-0.left-0 {
    z-index: 10;
}

/* Add some space between icon and text */
#search-input:focus::placeholder {
    color: transparent;
}

/* Page length selector styling */
#page-length {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 0.25rem 2rem 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: #374151;
    background-color: #ffffff;
    cursor: pointer;
    transition: all 0.2s ease;
}

#page-length:focus {
    outline: none;
    border-color: #d1d5db;
}

#page-length:hover {
    border-color: #d1d5db;
}




