/**
 * Register the commands for the application.
 */
protected function commands(): void
{
    $this->load(__DIR__.'/Commands');

    require base_path('routes/console.php');
}

/**
 * Define the application's command schedule.
 */
protected function schedule(Schedule $schedule): void
{
    // $schedule->command('inspire')->hourly();
    $schedule->command('auth:clear-expired-reset-tokens')->hourly();
}