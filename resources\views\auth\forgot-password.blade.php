@extends('layouts.auth')

@section('title', 'Forgot Password')

@section('header', 'Forgot your password?')
@section('subheader', 'Enter your email and we\'ll send you a reset link')

@section('content')
<!-- Forgot Password Form -->
<form class="space-y-6" method="POST" action="{{ route('password.email') }}">
    @csrf

    <!-- Email Field -->
    <div>
        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
            Email address
        </label>
        <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                <i class="material-icons-round text-gray-400 text-lg">email</i>
            </div>
            <input id="email"
                   name="email"
                   type="email"
                   autocomplete="email"
                   required
                   value="{{ old('email') }}"
                   class="material-input-with-icon @error('email') material-input-error @enderror"
                   placeholder="Enter your email address">
        </div>
        @error('email')
            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
        @enderror
    </div>

    <!-- Submit Button -->
    <div>
        <button type="submit" class="w-full material-button material-button-primary flex justify-center items-center">
            <i class="material-icons-round mr-2">send</i>
            Send reset link
        </button>
    </div>
</form>

<!-- Back to Login Link -->
<div class="mt-6 text-center">
    <p class="text-sm text-gray-600">
        Remember your password?
        <a href="{{ route('login') }}" class="font-medium text-blue-600 hover:text-blue-500 transition-colors duration-200">
            Back to login
        </a>
    </p>
</div>
@endsection


