<header class="bg-white shadow-sm border-b border-gray-200">
    <div class="flex items-center justify-between h-16 px-6">
        <!-- Left Section: Mobile Menu Button & Breadcrumbs -->
        <div class="flex items-center space-x-4">
            <!-- Mobile menu button -->
            <button id="mobile-menu-btn"
                    class="md:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500">
                <i class="material-icons">menu</i>
            </button>

            <!-- Breadcrumbs -->
            <nav class="hidden md:flex" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-2">
                    @if(isset($breadcrumbs) && count($breadcrumbs) > 0)
                        @foreach($breadcrumbs as $index => $breadcrumb)
                            <li class="flex items-center">
                                @if($index > 0)
                                    <i class="material-icons text-gray-400 mx-2">chevron_right</i>
                                @endif
                                @if($loop->last)
                                    <span class="text-gray-500 font-medium">{{ $breadcrumb['title'] }}</span>
                                @else
                                    <a href="{{ $breadcrumb['url'] }}" class="text-blue-600 hover:text-blue-800 font-medium">
                                        {{ $breadcrumb['title'] }}
                                    </a>
                                @endif
                            </li>
                        @endforeach
                    @else
                        <li>
                            <span class="text-gray-500 font-medium">@yield('title', 'Dashboard')</span>
                        </li>
                    @endif
                </ol>
            </nav>
        </div>

        <!-- Right Section: Search, Notifications, Profile -->
        <div class="flex items-center space-x-4">
            <!-- Search -->
            <div class="hidden lg:block relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="material-icons text-gray-400">search</i>
                </div>
                <input type="text"
                       placeholder="Search..."
                       class="block w-64 pl-10 pr-3 py-1.5 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:border-gray-400">
            </div>

            <!-- Visit Website -->
            <a href="{{ route('home') }}" 
               class="relative p-2 text-gray-400 hover:text-gray-500  rounded-full me-2"
               title="Visit Website">
                <i class="material-icons">public</i>
            </a>

            <!-- Notifications -->
            <div class="relative" data-header-dropdown>
                <button data-header-dropdown-button
                        class="relative p-2 text-gray-400 hover:text-gray-500 cursor-pointer rounded-full">
                    <i class="material-icons">notifications</i>
                    <!-- Notification badge -->
                    <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white"></span>
                </button>

                <!-- Notifications dropdown -->
                <div data-header-dropdown-menu class="hidden absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200 transition-all duration-100">
                    
                    <div class="px-4 py-3 border-b border-gray-200">
                        <h3 class="text-sm font-medium text-gray-900">Notifications</h3>
                    </div>
                    
                    <div class="max-h-64 overflow-y-auto">
                        <!-- Sample notifications -->
                        <a href="#" class="block px-4 py-3 hover:bg-gray-50 border-b border-gray-100">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <i class="material-icons text-blue-600 text-sm">person_add</i>
                                    </div>
                                </div>
                                <div class="ml-3 flex-1">
                                    <p class="text-sm text-gray-900">New user registered</p>
                                    <p class="text-xs text-gray-500 mt-1">2 minutes ago</p>
                                </div>
                            </div>
                        </a>
                        
                        <a href="#" class="block px-4 py-3 hover:bg-gray-50 border-b border-gray-100">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                        <i class="material-icons text-green-600 text-sm">check_circle</i>
                                    </div>
                                </div>
                                <div class="ml-3 flex-1">
                                    <p class="text-sm text-gray-900">System backup completed</p>
                                    <p class="text-xs text-gray-500 mt-1">1 hour ago</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    
                    <div class="px-4 py-3 border-t border-gray-200">
                        <a href="#" class="text-sm text-blue-600 hover:text-blue-800">View all notifications</a>
                    </div>
                </div>
            </div>

            <!-- Profile Dropdown -->
            <div class="relative" data-header-dropdown>
                <button data-header-dropdown-button
                        class="flex items-center space-x-3 text-sm rounded-full cursor-pointer">
                    @if(auth()->check() && auth()->user()->profile_image)
                        <img src="{{ asset('storage/' . auth()->user()->profile_image) }}"
                             alt="{{ auth()->user()->name }}"
                             class="w-8 h-8 rounded-full object-cover">
                    @else
                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                            <span class="text-white font-medium text-sm">
                                {{ auth()->check() ? substr(auth()->user()->name, 0, 1) : 'A' }}
                            </span>
                        </div>
                    @endif
                    <div class="hidden md:block text-left">
                        <p class="text-sm font-medium text-gray-900">
                            {{ auth()->check() ? auth()->user()->name : 'Admin User' }}
                        </p>
                        <p class="text-xs text-gray-500">Administrator</p>
                    </div>
                    <i class="material-icons text-gray-400">keyboard_arrow_down</i>
                </button>

                <!-- Profile dropdown menu -->
                <div data-header-dropdown-menu class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200 transition-all duration-100">
                    
                    <a href="{{ route('admin.profile.show') }}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="material-icons mr-3 text-sm">person</i>
                        Your Profile
                    </a>
                    <div class="border-t border-gray-100"></div>
                    <form method="POST" action="{{ route('logout') ?? '#' }}">
                        @csrf
                        <button type="submit" class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="material-icons mr-3 text-sm">logout</i>
                            Sign out
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</header>
