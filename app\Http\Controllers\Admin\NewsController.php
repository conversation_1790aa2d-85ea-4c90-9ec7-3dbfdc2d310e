<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\News;
use App\Models\Category;
use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Yajra\DataTables\Facades\DataTables;
use App\Helpers\DataTableHelper;

class NewsController extends Controller
{
    /**
     * Display a listing of news and handle datatable requests.
     */
    public function index(Request $request)
    {
        // Check if this is an AJAX request for datatable
        if ($request->ajax()) {
            // Only show approved news in the main news index
            $query = News::with(['categories', 'tags'])
                ->where(function($q) {
                    $q->where('approval_status', 'approved')
                      ->orWhereNull('approval_status'); // Include manually created news (no approval needed)
                });

            return DataTables::of($query)
                ->addColumn('news', function ($news) {
                    $imageHtml = '<img src="' . $news->main_image_url . '" alt="' . $news->title . '" class="w-12 h-12 object-cover rounded-lg mr-3">';
                    $titleHtml = '<div class="font-medium text-gray-900">' . $news->title . '</div>';
                    $slugHtml = '<div class="text-sm text-gray-500">' . $news->slug . '</div>';
                    
                    return '<div class="flex items-center">' . $imageHtml . '<div>' . $titleHtml . $slugHtml . '</div></div>';
                })
                ->addColumn('categories', function ($news) {
                    return DataTableHelper::relatedItemsList($news->categories);
                })
                ->addColumn('tags', function ($news) {
                    return DataTableHelper::relatedItemsList($news->tags);
                })
                ->addColumn('status', function ($news) {
                    $isActive = $news->status === 'active';
                    return DataTableHelper::statusBadge(
                        $isActive,
                        'Active',
                        'Inactive',
                        'verified',
                        'unverified'
                    );
                })
                ->addColumn('featured', function ($news) {
                    return DataTableHelper::statusBadge(
                        $news->is_featured,
                        'Featured',
                        'Regular',
                        'admin',
                        'default'
                    );
                })
                ->addColumn('published_at', function ($news) {
                    return $news->published_at ? $news->published_at->format('M d, Y H:i') : '-';
                })
                ->addColumn('actions', function ($news) {
                    return DataTableHelper::actionsColumn($news, 'admin.news', [
                        'viewTitle' => 'View News',
                        'editTitle' => 'Edit News',
                        'deleteTitle' => 'Delete News'
                    ]);
                })
                ->editColumn('created_at', function ($news) {
                    return $news->created_at->format('M d, Y');
                })
                ->rawColumns(['news', 'categories', 'tags', 'status', 'featured', 'actions'])
                ->make(true);
        }
        
        return view('admin.news.index');
    }

    /**
     * Show the form for creating a new news.
     */
    public function create()
    {
        $categories = Category::active()->orderBy('name')->get();
        $tags = Tag::active()->orderBy('name')->get();
        return view('admin.news.create', compact('categories', 'tags'));
    }

    /**
     * Store a newly created news in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => ['required', 'string', 'max:255'],
            'slug' => ['nullable', 'string', 'max:255', 'unique:news,slug'],
            'description' => ['required', 'string'],
            'main_image' => ['required', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
            'sub_image' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
            'status' => ['required', 'in:active,inactive'],
            'published_at' => ['nullable', 'date'],
            'sort_order' => ['nullable', 'integer', 'min:0'],
            'is_featured' => ['boolean'],
            'categories' => ['required', 'array'],
            'categories.*' => ['exists:categories,id'],
            'tags' => ['nullable', 'array'],
            'tags.*' => ['exists:tags,id'],
            'meta_title' => ['nullable', 'string', 'max:255'],
            'meta_description' => ['nullable', 'string', 'max:500'],
            'meta_keywords' => ['nullable', 'string'],
            'canonical_url' => ['nullable', 'url'],
            'robots_meta' => ['nullable', 'string'],
            'og_title' => ['nullable', 'string', 'max:255'],
            'og_description' => ['nullable', 'string', 'max:500'],
            'og_image' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
            'og_type' => ['nullable', 'string'],
        ]);

        $newsData = $request->except(['main_image', 'sub_image', 'og_image', 'categories', 'tags']);

        // Generate slug if not provided
        if (empty($newsData['slug'])) {
            $newsData['slug'] = Str::slug($newsData['title']);
        }

        // Manually created news is automatically approved
        $newsData['approval_status'] = 'approved';
        $newsData['approved_at'] = now();
        $newsData['approved_by'] = auth()->id();

        // Handle main image upload (required)
        if ($request->hasFile('main_image')) {
            $mainImagePath = $request->file('main_image')->store('news/main-images', 'public');
            $newsData['main_image'] = $mainImagePath;
        }

        // Handle sub image upload (optional)
        if ($request->hasFile('sub_image')) {
            $subImagePath = $request->file('sub_image')->store('news/sub-images', 'public');
            $newsData['sub_image'] = $subImagePath;
        }

        // Handle OG image upload
        if ($request->hasFile('og_image')) {
            $ogImagePath = $request->file('og_image')->store('news/og-images', 'public');
            $newsData['og_image'] = $ogImagePath;
        }

        $news = News::create($newsData);
        
        // Attach categories and tags
        $news->categories()->attach($request->categories);
        if ($request->tags) {
            $news->tags()->attach($request->tags);
        }
        
        return redirect()->route('admin.news.index')->with('success', 'News created successfully.');
    }

    /**
     * Display the specified news.
     */
    public function show(News $news)
    {
        $news->load(['categories', 'tags']);
        return view('admin.news.show', compact('news'));
    }

    /**
     * Show the form for editing the specified news.
     */
    public function edit(News $news)
    {
        $categories = Category::active()->orderBy('name')->get();
        $tags = Tag::active()->orderBy('name')->get();
        $news->load(['categories', 'tags']);

        return view('admin.news.edit', compact('news', 'categories', 'tags'));
    }

    /**
     * Update the specified news in storage.
     */
    public function update(Request $request, News $news)
    {
        // Custom validation for main image removal
        $request->validate([
            'title' => ['required', 'string', 'max:255'],
            'slug' => ['nullable', 'string', 'max:255', 'unique:news,slug,' . $news->id],
            'description' => ['required', 'string'],
            'main_image' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
            'sub_image' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
            'status' => ['required', 'in:active,inactive'],
            'published_at' => ['nullable', 'date'],
            'sort_order' => ['nullable', 'integer', 'min:0'],
            'is_featured' => ['boolean'],
            'categories' => ['required', 'array'],
            'categories.*' => ['exists:categories,id'],
            'tags' => ['nullable', 'array'],
            'tags.*' => ['exists:tags,id'],
            'meta_title' => ['nullable', 'string', 'max:255'],
            'meta_description' => ['nullable', 'string', 'max:500'],
            'meta_keywords' => ['nullable', 'string'],
            'canonical_url' => ['nullable', 'url'],
            'robots_meta' => ['nullable', 'string'],
            'og_title' => ['nullable', 'string', 'max:255'],
            'og_description' => ['nullable', 'string', 'max:500'],
            'og_image' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
            'og_type' => ['nullable', 'string'],
            'remove_main_image' => ['boolean'],
            'remove_sub_image' => ['boolean'],
            'remove_og_image' => ['boolean'],
        ]);

        // Validate that main image cannot be removed without uploading a new one
        if ($request->boolean('remove_main_image') && !$request->hasFile('main_image')) {
            return back()->withErrors([
                'remove_main_image' => 'Cannot remove main image without uploading a new one. Main image is required for news articles.'
            ])->withInput();
        }

        $newsData = $request->except(['main_image', 'sub_image', 'og_image', 'categories', 'tags', 'remove_main_image', 'remove_sub_image', 'remove_og_image']);

        // Generate slug if not provided
        if (empty($newsData['slug'])) {
            $newsData['slug'] = Str::slug($newsData['title']);
        }

        // Handle main image removal (only if new image is being uploaded)
        if ($request->boolean('remove_main_image') && $request->hasFile('main_image') && $news->main_image) {
            Storage::disk('public')->delete($news->main_image);
            // Don't set to null here, let the new image upload handle it
        }

        // Handle sub image removal
        if ($request->boolean('remove_sub_image') && $news->sub_image) {
            Storage::disk('public')->delete($news->sub_image);
            $newsData['sub_image'] = null;
        }

        // Handle OG image removal
        if ($request->boolean('remove_og_image') && $news->og_image) {
            Storage::disk('public')->delete($news->og_image);
            $newsData['og_image'] = null;
        }

        // Handle main image upload
        if ($request->hasFile('main_image')) {
            if ($news->main_image) {
                Storage::disk('public')->delete($news->main_image);
            }
            $mainImagePath = $request->file('main_image')->store('news/main-images', 'public');
            $newsData['main_image'] = $mainImagePath;
        }

        // Handle sub image upload
        if ($request->hasFile('sub_image')) {
            if ($news->sub_image) {
                Storage::disk('public')->delete($news->sub_image);
            }
            $subImagePath = $request->file('sub_image')->store('news/sub-images', 'public');
            $newsData['sub_image'] = $subImagePath;
        }

        // Handle OG image upload
        if ($request->hasFile('og_image')) {
            if ($news->og_image) {
                Storage::disk('public')->delete($news->og_image);
            }
            $ogImagePath = $request->file('og_image')->store('news/og-images', 'public');
            $newsData['og_image'] = $ogImagePath;
        }

        $news->update($newsData);

        // Sync categories and tags
        $news->categories()->sync($request->categories);
        $news->tags()->sync($request->tags ?? []);

        return redirect()->route('admin.news.index')->with('success', 'News updated successfully.');
    }

    /**
     * Remove the specified news from storage.
     */
    public function destroy(News $news)
    {
        // Delete associated images
        if ($news->main_image) {
            Storage::disk('public')->delete($news->main_image);
        }
        if ($news->sub_image) {
            Storage::disk('public')->delete($news->sub_image);
        }
        if ($news->og_image) {
            Storage::disk('public')->delete($news->og_image);
        }

        $news->delete();

        return redirect()->route('admin.news.index')->with('success', 'News deleted successfully.');
    }
}
