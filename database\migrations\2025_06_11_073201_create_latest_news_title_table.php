<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('latest_news_title', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('category');
            $table->string('published_by');
            $table->text('source_url')->nullable();
            $table->text('description')->nullable();
            $table->timestamp('source_published_at')->nullable();
            $table->boolean('is_processed')->default(false);
            $table->boolean('is_generated')->default(false);
            $table->unsignedBigInteger('generated_news_id')->nullable();
            $table->text('api_response')->nullable(); // Store original API response
            $table->timestamps();

            // Indexes for performance
            $table->index(['title', 'category']);
            $table->index('is_processed');
            $table->index('is_generated');
            $table->index('source_published_at');

            // Foreign key constraint
            $table->foreign('generated_news_id')->references('id')->on('news')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('latest_news_title');
    }
};
