<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Route;

class DataTableHelper
{
    /**
     * Generate actions column HTML for DataTables
     * 
     * @param mixed $model The model instance
     * @param string $routePrefix The route prefix (e.g., 'admin.users')
     * @param array $options Additional options for customizing actions
     * @return string HTML for the actions column
     */
    public static function actionsColumn($model, string $routePrefix, array $options = [])
    {
        // Default options
        $defaultOptions = [
            'show' => true,
            'edit' => true,
            'delete' => true,
            'extraButtons' => [],
            'deleteCondition' => null,
            'containerClasses' => 'flex items-center justify-end space-x-2 me-2',
            'viewClasses' => 'action-btn view',
            'editClasses' => 'action-btn edit',
            'deleteClasses' => 'action-btn delete',
            'viewIcon' => 'visibility',
            'editIcon' => 'edit',
            'deleteIcon' => 'delete',
            'viewTitle' => 'View',
            'editTitle' => 'Edit',
            'deleteTitle' => 'Delete',
            'deleteConfirmation' => false,
            'deleteConfirmationMessage' => 'Are you sure you want to delete this item? This action cannot be undone.',
        ];

        // Merge provided options with defaults
        $options = array_merge($defaultOptions, $options);

        // Extract model ID
        $modelId = $model->id;

        // Initialize actions HTML
        $actions = '<div class="' . $options['containerClasses'] . '">';

        // View button
        if ($options['show'] && Route::has("$routePrefix.show")) {
            $viewUrl = route("$routePrefix.show", $modelId);
            $actions .= '<a href="' . $viewUrl . '" class="' . $options['viewClasses'] . '" title="' . $options['viewTitle'] . '">
                            <i class="material-icons text-sm">' . $options['viewIcon'] . '</i>
                        </a>';
        }

        // Edit button
        if ($options['edit'] && Route::has("$routePrefix.edit")) {
            $editUrl = route("$routePrefix.edit", $modelId);
            $actions .= '<a href="' . $editUrl . '" class="' . $options['editClasses'] . '" title="' . $options['editTitle'] . '">
                            <i class="material-icons text-sm">' . $options['editIcon'] . '</i>
                        </a>';
        }

        // Delete button
        if ($options['delete'] && Route::has("$routePrefix.destroy")) {
            // Check if delete condition is provided and evaluates to false
            if ($options['deleteCondition'] !== null && !$options['deleteCondition']($model)) {
                // Skip delete button
            } else {
                $deleteUrl = route("$routePrefix.destroy", $modelId);
                $confirmAttr = $options['deleteConfirmation'] ? 'onsubmit="return confirm(\'' . $options['deleteConfirmationMessage'] . '\')"' : '';

                $actions .= '<form method="POST" action="' . $deleteUrl . '" class="inline delete-form" ' . $confirmAttr . '>
                                <input type="hidden" name="_token" value="' . csrf_token() . '">
                                <input type="hidden" name="_method" value="DELETE">
                                <button type="submit" class="' . $options['deleteClasses'] . '" title="' . $options['deleteTitle'] . '">
                                    <i class="material-icons text-sm">' . $options['deleteIcon'] . '</i>
                                </button>
                            </form>';
            }
        }

        // Add any extra buttons
        foreach ($options['extraButtons'] as $button) {
            $actions .= $button;
        }

        $actions .= '</div>';

        return $actions;
    }

    /**
     * Generate user avatar with profile image or initials fallback
     *
     * @param mixed $user The user model instance
     * @param string $nameField The field containing the user's name
     * @param array $options Additional options for customizing the avatar
     * @return string HTML for the user avatar
     */
    public static function userAvatar($user, string $nameField = 'name', array $options = [])
    {
        // Default options
        $defaultOptions = [
            'colors' => ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe'],
            'containerClasses' => 'flex items-center',
            'avatarClasses' => 'user-avatar mr-3',
            'nameClasses' => 'font-semibold text-gray-900',
            'showName' => true,
            'showEmail' => false,
            'emailField' => 'email',
            'emailClasses' => 'text-xs text-gray-500 mt-1',
            'imageField' => 'profile_image',
            'size' => '40px',
        ];

        // Merge provided options with defaults
        $options = array_merge($defaultOptions, $options);

        // Get user name
        $name = $user->{$nameField} ?? 'User';

        // Check if user has profile image
        $profileImage = $user->{$options['imageField']} ?? null;

        if ($profileImage) {
            // Use profile image
            $imageUrl = asset('storage/' . $profileImage);
            $avatarHtml = '<img src="' . $imageUrl . '" alt="' . $name . '" class="' . $options['avatarClasses'] . '" style="width: ' . $options['size'] . '; height: ' . $options['size'] . '; border-radius: 50%; object-fit: cover;">';
        } else {
            // Generate initials fallback
            $initials = collect(explode(' ', $name))->map(function ($segment) {
                return $segment[0] ?? '';
            })->join('');

            // Convert to uppercase
            if (function_exists('mb_strtoupper')) {
                $initials = mb_strtoupper($initials);
            } else {
                $initials = strtoupper($initials);
            }

            // Generate color based on name
            $colors = $options['colors'];
            $color = $colors[ord($name[0] ?? 'A') % count($colors)];

            $avatarHtml = '<div class="' . $options['avatarClasses'] . '" style="background: ' . $color . '; width: ' . $options['size'] . '; height: ' . $options['size'] . '; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 14px;">
                            ' . $initials . '
                          </div>';
        }

        // Build HTML
        $html = '<div class="' . $options['containerClasses'] . '">
                    ' . $avatarHtml;

        if ($options['showName'] || $options['showEmail']) {
            $html .= '<div>';

            if ($options['showName']) {
                $html .= '<div class="' . $options['nameClasses'] . '">' . $name . '</div>';
            }

            if ($options['showEmail']) {
                $email = $user->{$options['emailField']} ?? '';
                $html .= '<div class="' . $options['emailClasses'] . '">' . $email . '</div>';
            }

            $html .= '</div>';
        }

        $html .= '</div>';

        return $html;
    }

    /**
     * Generate status badge HTML
     * 
     * @param bool $condition The condition to check
     * @param string $trueText Text to display when condition is true
     * @param string $falseText Text to display when condition is false
     * @param string $trueClass CSS class for true condition
     * @param string $falseClass CSS class for false condition
     * @return string HTML for the status badge
     */
    public static function statusBadge($condition, $trueText = 'Active', $falseText = 'Inactive', $trueClass = 'verified', $falseClass = 'unverified')
    {
        if ($condition) {
            return '<span class="status-badge ' . $trueClass . '">' . $trueText . '</span>';
        } else {
            return '<span class="status-badge ' . $falseClass . '">' . $falseText . '</span>';
        }
    }

    /**
     * Generate role badge HTML
     * 
     * @param string $role The role name
     * @param array $roleClasses Associative array mapping roles to CSS classes
     * @return string HTML for the role badge
     */
    public static function roleBadge($role, array $roleClasses = [])
    {
        $defaultClasses = [
            'admin' => 'admin',
            'administrator' => 'admin',
            'user' => 'user',
            'editor' => 'editor',
            'moderator' => 'moderator',
            'subscriber' => 'subscriber',
        ];

        $roleClasses = array_merge($defaultClasses, $roleClasses);
        $lowerRole = strtolower($role);
        $class = $roleClasses[$lowerRole] ?? 'default';

        return '<span class="role-badge ' . $class . '">' . $role . '</span>';
    }

    /**
     * Format a list of related items with a "more" indicator
     * 
     * @param mixed $collection Collection of related items
     * @param string $nameField Field to use for item names
     * @param int $limit Maximum number of items to show
     * @return string Formatted HTML
     */
    public static function relatedItemsList($collection, $nameField = 'name', $limit = 3)
    {
        $itemsList = $collection->take($limit)->pluck($nameField)->implode(', ');
        $moreCount = $collection->count() > $limit ? ' +' . ($collection->count() - $limit) . ' more' : '';

        return '<div class="flex items-center">
                    <div class="text-gray-900 text-sm">' . ($itemsList ?: 'None') . $moreCount . '</div>
                </div>';
    }
}
