<?php
namespace App\Providers;

use App\Models\Permission;
use App\Models\User;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Schema;

class AuthServiceProvider extends ServiceProvider
{
    
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        // Define your policies here
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        // Register the Gate::before callback first
        Gate::before(function (User $user) {
            // Super admin has all permissions
            if ($user->hasRole('developer')) {
                return true;
            }
        });

        
        // Register other permissions as gates
        $this->registerPermissions();
        
        // For debugging
    }

    /**
     * Register permissions as gates
     */
    protected function registerPermissions(): void
    {
        try {
            // Only register permissions if the table exists and we're not in the console
            if (!Schema::hasTable('permissions') || app()->runningInConsole()) {
                return;
            }

            // Register each permission as a gate
            Permission::all()->each(function ($permission) {
                $slug = $permission->slug;
                
                Gate::define($slug, function (User $user) use ($slug) {
                    $hasPermission = $user->hasPermission($slug);
                    return $hasPermission;
                });
            });
        } catch (\Exception $e) {
            // Handle exceptions (like during migrations)
            report($e);
        }
    }
}






