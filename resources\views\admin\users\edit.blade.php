@extends('layouts.admin')

@section('title', 'Edit User')

@php
$pageTitle = 'Edit User';
$pageDescription = 'Modify user details';
$breadcrumbs = [
    ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
    ['title' => 'Users', 'url' => route('admin.users.index')],
    ['title' => 'Edit', 'url' => '#']
];
@endphp

@section('page-header')
<div class="flex items-center justify-between">
    <div>
        <h1 class="text-3xl font-semibold text-gray-900">{{ $pageTitle }}</h1>
        <p class="mt-2 text-gray-600">{{ $pageDescription }}</p>
    </div>
    <div class="flex space-x-3">
        <a href="{{ route('admin.users.show', $user) }}" class="material-button material-button-md material-button-secondary flex items-center">
            <i class="material-icons mr-2">visibility</i>
            View User
        </a>
        <a href="{{ route('admin.users.index') }}" class="material-button material-button-md material-button-secondary flex items-center">
            <i class="material-icons mr-2">arrow_back</i>
            Back to Users
        </a>
    </div>
</div>
@endsection

@section('content')

<!-- Edit User Form -->
<div class="material-card">
    <form method="POST" action="{{ route('admin.users.update', $user) }}" class="p-6 space-y-6" id="user_edit_form" enctype="multipart/form-data">
        @csrf
        @method('PUT')

        <!-- Form Layout - Responsive Grid -->
        <div class="space-y-6">
            <!-- Row 1: Name and Email -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Name Field -->
                <div class="order-1 lg:order-1">
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Full Name <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">person</i>
                        </div>
                        <input id="name"
                            name="name"
                            type="text"
                            required
                            value="{{ old('name', $user->name) }}"
                            class="material-input-with-icon @error('name') material-input-error @enderror"
                            placeholder="Enter full name">
                    </div>
                    @error('name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Email Field -->
                <div class="order-2 lg:order-2">
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        Email Address <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">email</i>
                        </div>
                        <input id="email"
                            name="email"
                            type="email"
                            required
                            value="{{ old('email', $user->email) }}"
                            class="material-input-with-icon @error('email') material-input-error @enderror"
                            placeholder="Enter email address">
                    </div>
                    @error('email')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Row 2: Password and Confirm Password -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Password Field -->
                <div class="order-3 lg:order-1">
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        Password <small class="text-gray-500">(leave blank to keep current password)</small>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">lock</i>
                        </div>
                        <input id="password"
                            name="password"
                            type="password"
                            autocomplete="new-password"
                            class="material-input-with-icon @error('password') material-input-error @enderror"
                            placeholder="Enter new password">
                        <button type="button"
                            id="toggle-password-admin"
                            data-password-toggle="password:password-icon-admin"
                            class="absolute inset-y-0 right-0 px-3 flex items-center z-10 focus:outline-none rounded">
                            <i class="material-icons text-gray-400 hover:text-gray-600 text-lg" id="password-icon-admin">visibility</i>
                        </button>
                    </div>
                    @error('password')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Confirm Password Field -->
                <div class="order-4 lg:order-2">
                    <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">
                        Confirm Password
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">lock</i>
                        </div>
                        <input id="password_confirmation"
                            name="password_confirmation"
                            type="password"
                            class="material-input-with-icon"
                            placeholder="Confirm new password">
                        <button type="button"
                            id="toggle-password-confirm-admin"
                            data-password-toggle="password_confirmation:password-confirm-icon-admin"
                            class="absolute inset-y-0 right-0 px-3 flex items-center z-10 focus:outline-none rounded">
                            <i class="material-icons text-gray-400 hover:text-gray-600 text-lg" id="password-confirm-icon-admin">visibility</i>
                        </button>
                    </div>
                    @error('password_confirmation')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Row 3: Role and Status -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Role Field -->
                <div class="order-5 lg:order-1">
                    <label for="role" class="block text-sm font-medium text-gray-700 mb-2">
                        Role <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">verified_user</i>
                        </div>
                        <select id="role"
                            name="role"
                            required
                            class="material-select-with-icon @error('role') material-select-error @enderror">
                            <option value="">Select a role</option>
                            @foreach($roles as $role)
                            <option value="{{ $role->id }}" {{ (old('role', $user->roles->first()->id ?? '') == $role->id) ? 'selected' : '' }}>
                                {{ $role->name }}
                            </option>
                            @endforeach
                        </select>
                    </div>
                    @error('role')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Status Field -->
                <div class="order-6 lg:order-2">
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                        Status
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                            <i class="material-icons text-gray-400 text-lg">toggle_on</i>
                        </div>
                        <select id="status"
                            name="status"
                            class="material-select-with-icon @error('status') material-select-error @enderror">
                            <option value="active" {{ old('status', $user->email_verified_at ? 'active' : 'inactive') == 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ old('status', $user->email_verified_at ? 'active' : 'inactive') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                    </div>
                    @error('status')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Row 4: Profile Image (Full Width) -->
            <div class="grid grid-cols-1 gap-6">
                <div class="order-7">
                    <label for="profile_image" class="block text-sm font-medium text-gray-700 mb-2">
                        Profile Image <small class="text-gray-500">(optional)</small>
                    </label>
                    <div class="mt-1 flex items-center space-x-4">
                        <!-- Image Preview -->
                        <div class="flex-shrink-0">
                            <img id="image-preview" class="h-20 w-20 rounded-full object-cover border-2 border-gray-300 bg-gray-100"
                                 src="{{ $user->profile_image_url }}"
                                 alt="Profile preview">
                        </div>
                        <!-- File Input and Actions -->
                        <div class="flex-1">
                            <div class="flex flex-wrap gap-2 mb-2">
                                <div class="relative">
                                    <input id="profile_image"
                                           name="profile_image"
                                           type="file"
                                           accept="image/*"
                                           class="hidden"
                                           onchange="previewImage(this)">
                                    <label for="profile_image" class="material-button material-button-sm material-button-secondary cursor-pointer flex items-center">
                                        <i class="material-icons mr-2 text-sm">upload</i>
                                        {{ $user->profile_image ? 'Change Image' : 'Upload Image' }}
                                    </label>
                                </div>
                                @if($user->profile_image)
                                    <button type="button" onclick="removeImage()" class="material-button material-button-sm material-button-danger flex items-center">
                                        <i class="material-icons mr-2 text-sm">delete</i>
                                        Remove Image
                                    </button>
                                    <!-- Hidden input to track image removal -->
                                    <input type="hidden" id="remove_image" name="remove_image" value="0">
                                @endif
                            </div>
                            <p class="mt-1 text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
                            @if($user->profile_image)
                                <p class="mt-1 text-xs text-gray-600" id="current-image-info">Current: {{ basename($user->profile_image) }}</p>
                            @endif
                        </div>
                    </div>
                    @error('profile_image')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Admin Note -->
        <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="material-icons text-blue-400">info</i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">Password Information</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <p>Leave the password fields empty if you don't want to change the user's password.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            <a href="{{ route('admin.users.index') }}" class="material-button material-button-sm material-button-secondary flex items-center">
                Cancel
            </a>
            <button type="submit" class="material-button material-button-sm material-button-primary flex items-center">
                <i class="material-icons mr-2 text-sm">save</i>
                Update User
            </button>
        </div>
    </form>
</div>
@endsection

@push('scripts')
    @vite('resources/js/utils/common.js')
    <script src="https://code.jquery.com/jquery-3.7.0.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize password toggles
            initPasswordToggle('toggle-password-admin', 'password', 'password-icon-admin');
            initPasswordToggle('toggle-password-confirm-admin', 'password_confirmation', 'password-confirm-icon-admin');

            // jQuery Form validation
            $("#user_edit_form").validate({
                rules: {
                    name: {
                        required: true,
                        minlength: 2,
                        maxlength: 255
                    },
                    email: {
                        required: true,
                        email: true,
                        maxlength: 255
                    },
                    password: {
                        minlength: 8,
                        maxlength: 255
                    },
                    password_confirmation: {
                        equalTo: "#password"
                    },
                    role: {
                        required: true
                    },
                    status: {
                        required: true
                    },
                    profile_image: {
                        extension: "jpg|jpeg|png|gif",
                        filesize: 2097152 // 2MB in bytes
                    }
                },
                messages: {
                    name: {
                        required: "Please enter the user's full name",
                        minlength: "Name must be at least 2 characters",
                        maxlength: "Name cannot exceed 255 characters"
                    },
                    email: {
                        required: "Please enter an email address",
                        email: "Please enter a valid email address",
                        maxlength: "Email cannot exceed 255 characters"
                    },
                    password: {
                        minlength: "Password must be at least 8 characters",
                        maxlength: "Password cannot exceed 255 characters"
                    },
                    password_confirmation: {
                        equalTo: "Passwords do not match"
                    },
                    role: {
                        required: "Please select a role"
                    },
                    status: {
                        required: "Please select a status"
                    },
                    profile_image: {
                        extension: "Please select a valid image file (JPG, JPEG, PNG, GIF)",
                        filesize: "Image file size must be less than 2MB"
                    }
                },
                errorElement: 'span',
                errorPlacement: function (error, element) {
                    error.addClass("text-red-500 text-xs mt-1").insertAfter(element.closest('.relative'));
                },
                highlight: function (element) {
                    $(element).addClass('border-red-500').removeClass('border-gray-300');
                },
                unhighlight: function (element) {
                    $(element).removeClass('border-red-500').addClass('border-gray-300');
                },
                // Custom validation for password fields in edit form
                ignore: function(index, element) {
                    // Don't validate password confirmation if password is empty (edit form)
                    if (element.name === 'password_confirmation' && $('#password').val() === '') {
                        return true;
                    }
                    return false;
                }
            });

            // Custom validation methods
            $.validator.addMethod("filesize", function(value, element, param) {
                return this.optional(element) || (element.files[0] && element.files[0].size <= param);
            }, "File size must be less than {0} bytes");

            // Dynamic password validation for edit form
            $('#password').on('input', function() {
                const passwordValue = $(this).val();
                const confirmField = $('#password_confirmation');

                if (passwordValue === '') {
                    // If password is cleared, remove required validation from confirmation
                    confirmField.rules('remove', 'required');
                } else {
                    // If password has value, add required validation to confirmation
                    confirmField.rules('add', {
                        required: true,
                        messages: {
                            required: "Please confirm the new password"
                        }
                    });
                }
            });
        });
    </script>
@endpush


