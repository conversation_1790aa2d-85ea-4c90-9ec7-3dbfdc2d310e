<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Role;
use App\Models\User;
use App\Providers\AuthServiceProvidert;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;
use App\Helpers\DataTableHelper;

class UserController extends Controller
{
    /**
     * Display a listing of users and handle datatable requests.
     */
    public function index(Request $request)
    {
        // Check if this is an AJAX request for datatable
        if ($request->ajax()) {
            $query = User::query()->where('id', '!=', auth()->id()); // Exclude logged-in user
            
            return DataTables::of($query)
                ->addColumn('user', function ($user) {
                    return DataTableHelper::userAvatar($user);
                })
                ->addColumn('status', function ($user) {
                    $isActive = $user->status === 'active';
                    return DataTableHelper::statusBadge(
                        $isActive, 
                        'Active', 
                        'Inactive', 
                        'verified', 
                        'unverified'
                    );
                })
                ->addColumn('role', function ($user) {
                    // Eager load the roles relationship if not already loaded
                    if (!$user->relationLoaded('roles')) {
                        $user->load('roles');
                    }
                    
                    // Get the first role or default to "User" if no roles assigned
                    $role = $user->roles->first();
                    $roleName = $role ? $role->name : 'User';
                    
                    return DataTableHelper::roleBadge($roleName);
                })
                ->addColumn('actions', function ($user) {
                    $currentUserId = auth()->id();
                    
                    return DataTableHelper::actionsColumn($user, 'admin.users', [
                        'deleteCondition' => function($model) use ($currentUserId) {
                            return $model->id != $currentUserId;
                        },
                        'viewTitle' => 'View User',
                        'editTitle' => 'Edit User',
                        'deleteTitle' => 'Delete User'
                    ]);
                })
                ->editColumn('created_at', function ($user) {
                    return $user->created_at->format('M d, Y');
                })
                ->rawColumns(['user', 'status', 'role', 'actions'])
                ->make(true);
        }
        
        return view('admin.users.index');
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        $roles = Role::all();
        return view('admin.users.create', compact('roles'));
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'role' => ['required', 'exists:roles,id'],
            'profile_image' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
        ]);

        $userData = [
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ];

        // Handle profile image upload
        if ($request->hasFile('profile_image')) {
            $profileImage = $request->file('profile_image');
            $imageName = time() . '_' . $profileImage->getClientOriginalName();
            $imagePath = $profileImage->storeAs('profile_images', $imageName, 'public');
            $userData['profile_image'] = $imagePath;
        }

        $user = User::create($userData);

        // Assign role to user
        $user->roles()->attach($request->role);

        return redirect()->route('admin.users.index')->with('success', 'User created successfully.');
    }

    /**
     * Display the specified user.
     */
    public function show(User $user)
    {
        return view('admin.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(User $user)
    {
        $roles = Role::all();
        return view('admin.users.edit', compact('user', 'roles'));
    }

    /**
     * Update the specified user in storage.
     */
    public function update(Request $request, User $user)
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'role' => ['required', 'exists:roles,id'],
            'status' => ['required', 'in:active,inactive'],
            'profile_image' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
        ];
        if ($request->filled('password')) {
            $rules['password'] = ['string', 'min:8', 'confirmed'];
        }
        $request->validate($rules);

        // Update user details
        $user->name = $request->name;
        $user->email = $request->email;
        $user->status = $request->status;

        if ($request->filled('password')) {
            $user->password = Hash::make($request->password);
        }

        // Handle profile image upload or removal
        if ($request->has('remove_image') && $request->remove_image == '1') {
            // Remove existing image
            if ($user->profile_image && Storage::disk('public')->exists($user->profile_image)) {
                Storage::disk('public')->delete($user->profile_image);
            }
            $user->profile_image = null;
        } elseif ($request->hasFile('profile_image')) {
            // Upload new image
            // Delete old profile image if exists
            if ($user->profile_image && Storage::disk('public')->exists($user->profile_image)) {
                Storage::disk('public')->delete($user->profile_image);
            }

            $profileImage = $request->file('profile_image');
            $imageName = time() . '_' . $profileImage->getClientOriginalName();
            $imagePath = $profileImage->storeAs('profile_images', $imageName, 'public');
            $user->profile_image = $imagePath;
        }

        $user->save();

        // Update role
        $user->roles()->sync([$request->role]);

        return redirect()->route('admin.users.index')
                        ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy(User $user)
    {
        // Prevent admin from deleting themselves
        if ($user->id === auth()->id()) {
            return redirect()->route('admin.users.index')->with('error', 'You cannot delete your own account.');
        }

        // Delete profile image if exists
        if ($user->profile_image && Storage::disk('public')->exists($user->profile_image)) {
            Storage::disk('public')->delete($user->profile_image);
        }

        $user->delete();

        return redirect()->route('admin.users.index')->with('success', 'User deleted successfully.');
    }
}
