<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Foundation\Auth\EmailVerificationRequest;

class EmailVerificationController extends Controller
{
    /**
     * Show the email verification notice.
     *
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function notice(Request $request)
    {
        // If user is already verified, redirect to dashboard
        if ($request->user()->hasVerifiedEmail()) {
            return redirect('/admin/dashboard');
        }
        
        return view('auth.verify-email');
    }

    /**
     * Mark the authenticated user's email address as verified.
     *
     * @param  \Illuminate\Foundation\Auth\EmailVerificationRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function verify(EmailVerificationRequest $request)
    {
        if ($request->user()->hasVerifiedEmail()) {
            return redirect('/admin/dashboard');
        }

        $request->fulfill();
        return redirect('/admin/dashboard')->with('status', 'Your email has been verified!');
    }

    /**
     * Send a new email verification notification.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function send(Request $request)
    {
        if ($request->user()->hasVerifiedEmail()) {
            return redirect('/admin/dashboard');
        }

        // Check if the last verification link was sent less than 60 seconds ago
        $lastSent = session('verification_link_sent_at');
        $cooldownTime = 60; // 60 seconds cooldown

        if ($lastSent && time() - $lastSent < $cooldownTime) {
            $remainingTime = $cooldownTime - (time() - $lastSent);
            return back()->with('error', "Please wait {$remainingTime} seconds before requesting another verification link.");
        }

        $request->user()->sendEmailVerificationNotification();
        // Store the current timestamp
        session(['verification_link_sent_at' => time()]);

        return back()->with('status', 'verification-link-sent');
    }
}


