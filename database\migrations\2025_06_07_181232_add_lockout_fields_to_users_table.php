<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->timestamp('locked_until')->nullable()->after('status');
            $table->integer('failed_login_attempts')->default(0)->after('locked_until');
            $table->timestamp('last_login_at')->nullable()->after('failed_login_attempts');
            $table->string('last_login_ip', 45)->nullable()->after('last_login_at');
            $table->boolean('is_deactivated')->default(false)->after('last_login_ip');
            $table->text('deactivation_reason')->nullable()->after('is_deactivated');
            $table->timestamp('deactivated_at')->nullable()->after('deactivation_reason');

            // Add indexes
            $table->index(['is_deactivated', 'status']);
            $table->index(['locked_until']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'locked_until',
                'failed_login_attempts',
                'last_login_at',
                'last_login_ip',
                'is_deactivated',
                'deactivation_reason',
                'deactivated_at'
            ]);
        });
    }
};
