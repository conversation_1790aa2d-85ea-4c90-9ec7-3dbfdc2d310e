<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Seeder;

class RolesAndPermissionsSeeder extends Seeder
{
    public function run(): void
    {
        // Create permissions
        $permissions = [
            ['name' => 'View Users', 'slug' => 'users_view'],
            ['name' => 'Create Users', 'slug' => 'users_create'],
            ['name' => 'Edit Users', 'slug' => 'users_edit'],
            ['name' => 'Delete Users', 'slug' => 'users_delete'],
            ['name' => 'View Dashboard', 'slug' => 'dashboard_view'],

            // News permissions
            ['name' => 'View News', 'slug' => 'news_view'],
            ['name' => 'Create News', 'slug' => 'news_create'],
            ['name' => 'Edit News', 'slug' => 'news_edit'],
            ['name' => 'Delete News', 'slug' => 'news_delete'],
            ['name' => 'Access News', 'slug' => 'news_access'],
        ];

        foreach ($permissions as $permission) {
            Permission::create($permission);
        }
    }
}
