<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\UserLoginLog;
use App\Models\LoginAttempt;
use App\Models\User;
use App\Services\LoginSecurityService;

class LoginLogController extends Controller
{
    /**
     * Display login logs and security dashboard
     */
    public function index(Request $request, LoginSecurityService $loginSecurity)
    {
        // Get filter parameters
        $type = $request->get('type', 'all');
        $email = $request->get('email');
        $hours = $request->get('hours', 24);

        // Build query for login logs
        $query = UserLoginLog::with('user')
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($type !== 'all') {
            $query->where('type', $type);
        }

        if ($email) {
            $query->where('email', 'like', "%{$email}%");
        }

        if ($hours) {
            $query->where('created_at', '>=', now()->subHours($hours));
        }

        $logs = $query->paginate(50);

        // Get security statistics
        $stats = $loginSecurity->getFailedLoginStats($hours);

        // Get current lockouts and deactivated accounts
        $lockedAttempts = LoginAttempt::locked()
            ->orderBy('locked_until', 'desc')
            ->get();

        $deactivatedAttempts = LoginAttempt::deactivated()
            ->orderBy('updated_at', 'desc')
            ->get();

        $deactivatedUsers = User::where('is_deactivated', true)
            ->orderBy('deactivated_at', 'desc')
            ->get();

        return view('admin.login-logs.index', compact(
            'logs',
            'stats',
            'lockedAttempts',
            'deactivatedAttempts',
            'deactivatedUsers',
            'type',
            'email',
            'hours'
        ));
    }

    /**
     * Clear login attempts for an email (admin action)
     */
    public function clearAttempts(Request $request, string $email, LoginSecurityService $loginSecurity)
    {
        try {
            $loginSecurity->clearLoginAttempts($email);

            return redirect()->route('admin.login-logs.index')
                ->with('success', "Login attempts cleared for {$email}");
        } catch (\Exception $e) {
            return redirect()->route('admin.login-logs.index')
                ->with('error', 'Failed to clear login attempts: ' . $e->getMessage());
        }
    }

    /**
     * Reactivate a deactivated account (admin action)
     */
    public function reactivateAccount(Request $request, string $email, LoginSecurityService $loginSecurity)
    {
        try {
            $success = $loginSecurity->reactivateAccount($email);

            if ($success) {
                return redirect()->route('admin.login-logs.index')
                    ->with('success', "Account reactivated for {$email}");
            } else {
                return redirect()->route('admin.login-logs.index')
                    ->with('error', "User not found: {$email}");
            }
        } catch (\Exception $e) {
            return redirect()->route('admin.login-logs.index')
                ->with('error', 'Failed to reactivate account: ' . $e->getMessage());
        }
    }
}
