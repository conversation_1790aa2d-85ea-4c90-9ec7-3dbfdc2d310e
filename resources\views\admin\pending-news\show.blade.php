@extends('layouts.admin')

@section('title', 'Review Pending News')

@push('styles')
<style>
    /* Custom styles for HTML content display */
    .news-content-display {
        line-height: 1.7;
    }
    .news-content-display h1 {
        font-size: 1.875rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: #1f2937;
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 0.5rem;
    }
    .news-content-display h2 {
        font-size: 1.5rem;
        font-weight: 600;
        margin: 1.5rem 0 0.75rem 0;
        color: #1f2937;
    }
    .news-content-display h3 {
        font-size: 1.25rem;
        font-weight: 600;
        margin: 1.25rem 0 0.5rem 0;
        color: #374151;
    }
    .news-content-display h4 {
        font-size: 1.125rem;
        font-weight: 600;
        margin: 1rem 0 0.5rem 0;
        color: #374151;
    }
    .news-content-display p {
        margin-bottom: 1rem;
        color: #374151;
    }
    .news-content-display ul, .news-content-display ol {
        margin: 1rem 0;
        padding-left: 1.5rem;
    }
    .news-content-display li {
        margin-bottom: 0.5rem;
        color: #374151;
    }
    .news-content-display ul li {
        list-style-type: disc;
    }
    .news-content-display ol li {
        list-style-type: decimal;
    }
    .news-content-display blockquote {
        border-left: 4px solid #3b82f6;
        padding-left: 1rem;
        margin: 1.5rem 0;
        font-style: italic;
        color: #6b7280;
        background-color: #f8fafc;
        padding: 1rem;
        border-radius: 0.375rem;
    }
    .news-content-display table {
        width: 100%;
        border-collapse: collapse;
        margin: 1.5rem 0;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        overflow: hidden;
    }
    .news-content-display th, .news-content-display td {
        border: 1px solid #e5e7eb;
        padding: 0.75rem;
        text-align: left;
    }
    .news-content-display th {
        background-color: #f9fafb;
        font-weight: 600;
        color: #374151;
    }
    .news-content-display td {
        color: #374151;
    }
    .news-content-display a {
        color: #3b82f6;
        text-decoration: underline;
        font-weight: 500;
    }
    .news-content-display a:hover {
        color: #1d4ed8;
    }
    .news-content-display strong {
        font-weight: 600;
        color: #1f2937;
    }
    .news-content-display em {
        font-style: italic;
        color: #6b7280;
    }
    .news-content-display code {
        background-color: #f3f4f6;
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
        font-family: 'Courier New', monospace;
        font-size: 0.875rem;
        color: #dc2626;
    }
</style>
@endpush

@php
    $pageTitle = 'Review Pending News';
    $pageDescription = 'Review AI-generated content before approval';
    $breadcrumbs = [
        ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
        ['title' => 'Pending News', 'url' => route('admin.pending-news.index')],
        ['title' => 'Review', 'url' => '#']
    ];
@endphp

@section('page-header')
<div class="flex items-center justify-between">
    <div>
        <h1 class="text-3xl font-semibold text-gray-900">{{ $pageTitle }}</h1>
        <p class="mt-2 text-gray-600">{{ $pageDescription }}</p>
    </div>
    <div class="flex items-center space-x-3">
        <button onclick="approveNews({{ $news->id }})" class="material-button material-button-primary flex items-center">
            <i class="material-icons mr-2">check_circle</i>
            Approve
        </button>
        <button onclick="rejectNews({{ $news->id }})" class="material-button material-button-danger flex items-center">
            <i class="material-icons mr-2">cancel</i>
            Reject
        </button>
        <a href="{{ route('admin.news.edit', $news) }}" class="material-button material-button-secondary flex items-center">
            <i class="material-icons mr-2">edit</i>
            Edit
        </a>
        <a href="{{ route('admin.pending-news.index') }}" class="material-button material-button-secondary flex items-center">
            <i class="material-icons mr-2">arrow_back</i>
            Back to List
        </a>
    </div>
</div>
@endsection

@section('content')
<div class="space-y-6">
    <!-- Approval Status Alert -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <i class="material-icons text-yellow-600">pending</i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">Pending Approval</h3>
                <p class="text-sm text-yellow-700 mt-1">
                    This AI-generated content is waiting for your review and approval before it goes live.
                </p>
            </div>
        </div>
    </div>

    <!-- Basic Information -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="material-icons mr-2 text-blue-600">article</i>
                Article Content
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                    <p class="text-gray-900 font-medium">{{ $news->title }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Slug</label>
                    <p class="text-gray-900 font-mono text-sm bg-gray-50 px-3 py-2 rounded-md">{{ $news->slug }}</p>
                </div>
                <div class="lg:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Generated Content</label>
                    <div class="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                        <div class="news-content-display">
                            {!! $news->description !!}
                        </div>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                        <i class="material-icons mr-1 text-xs">pending</i>
                        Pending Approval
                    </span>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Generated At</label>
                    <p class="text-gray-900">{{ $news->created_at->format('M d, Y H:i') }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Categories and Tags -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="material-icons mr-2 text-blue-600">label</i>
                Categories & Tags
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Categories</label>
                    @if($news->categories->count() > 0)
                        <div class="flex flex-wrap gap-2">
                            @foreach($news->categories as $category)
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                    {{ $category->name }}
                                </span>
                            @endforeach
                        </div>
                    @else
                        <p class="text-gray-500 text-sm">No categories assigned</p>
                    @endif
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Tags</label>
                    @if($news->tags->count() > 0)
                        <div class="flex flex-wrap gap-2">
                            @foreach($news->tags as $tag)
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium" 
                                      style="background-color: {{ $tag->color }}20; color: {{ $tag->color }};">
                                    {{ $tag->name }}
                                </span>
                            @endforeach
                        </div>
                    @else
                        <p class="text-gray-500 text-sm">No tags assigned</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- SEO Meta Information -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="material-icons mr-2 text-blue-600">search</i>
                SEO Meta Information
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Meta Title</label>
                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ $news->meta_title ?: 'Not set' }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Meta Description</label>
                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ $news->meta_description ?: 'Not set' }}</p>
                </div>
                <div class="lg:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Meta Keywords</label>
                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ $news->meta_keywords ?: 'Not set' }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Open Graph Information -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="material-icons mr-2 text-blue-600">share</i>
                Open Graph Information
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">OG Title</label>
                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ $news->og_title ?: 'Not set' }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">OG Description</label>
                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{{ $news->og_description ?: 'Not set' }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Rejection Modal -->
<div id="rejection-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Reject News Article</h3>
                <button id="close-rejection-modal" class="text-gray-400 hover:text-gray-600">
                    <i class="material-icons">close</i>
                </button>
            </div>
            <form id="rejection-form">
                <div class="mb-4">
                    <label for="rejection-reason" class="block text-sm font-medium text-gray-700 mb-2">
                        Rejection Reason <span class="text-red-500">*</span>
                    </label>
                    <textarea id="rejection-reason" name="reason" rows="4" required
                        class="material-textarea"
                        placeholder="Please provide a reason for rejecting this article..."></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancel-rejection" class="material-button material-button-sm material-button-secondary">
                        Cancel
                    </button>
                    <button type="submit" class="material-button material-button-sm material-button-danger">
                        Reject Article
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://code.jquery.com/jquery-3.7.0.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // Approve news function
    window.approveNews = function(id) {
        Swal.fire({
            title: 'Approve News Article?',
            text: 'This will make the article live on your website.',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#10b981',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, Approve',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '{{ route("admin.pending-news.approve", $news->id) }}',
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        Swal.fire('Approved!', response.message, 'success').then(() => {
                            window.location.href = '{{ route("admin.pending-news.index") }}';
                        });
                    },
                    error: function(xhr) {
                        const message = xhr.responseJSON?.message || 'Failed to approve news';
                        Swal.fire('Error!', message, 'error');
                    }
                });
            }
        });
    };

    // Reject news function
    window.rejectNews = function(id) {
        $('#rejection-modal').removeClass('hidden');
        $('#rejection-reason').val('').focus();
    };

    // Close rejection modal
    $('#close-rejection-modal, #cancel-rejection').on('click', function() {
        $('#rejection-modal').addClass('hidden');
    });

    // Handle rejection form submission
    $('#rejection-form').on('submit', function(e) {
        e.preventDefault();

        const reason = $('#rejection-reason').val().trim();
        if (!reason) {
            Swal.fire('Error!', 'Please provide a rejection reason', 'error');
            return;
        }

        $.ajax({
            url: '{{ route("admin.pending-news.reject", $news->id) }}',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                reason: reason
            },
            success: function(response) {
                $('#rejection-modal').addClass('hidden');
                Swal.fire('Rejected!', response.message, 'success').then(() => {
                    window.location.href = '{{ route("admin.pending-news.index") }}';
                });
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'Failed to reject news';
                Swal.fire('Error!', message, 'error');
            }
        });
    });

    // Close modal when clicking outside
    $('#rejection-modal').on('click', function(e) {
        if (e.target === this) {
            $(this).addClass('hidden');
        }
    });

    // Escape key to close modal
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            $('#rejection-modal').addClass('hidden');
        }
    });
});
</script>
@endpush
