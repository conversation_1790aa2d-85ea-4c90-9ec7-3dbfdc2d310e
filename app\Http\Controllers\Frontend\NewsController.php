<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\News;
use Illuminate\Http\Request;

class NewsController extends Controller
{
    public function show($slug)
    {
        $news = News::where('slug', $slug)
            ->published()
            ->firstOrFail();
            
        // Increment view count
        $news->increment('view_count');
        
        // Get related news from same categories
        $relatedNews = News::published()
            ->whereHas('categories', function($query) use ($news) {
                $query->whereIn('categories.id', $news->categories->pluck('id'));
            })
            ->where('id', '!=', $news->id)
            ->latest()
            ->limit(3)
            ->get();
            
        return view('frontend.news.show', compact('news', 'relatedNews'));
    }
}
