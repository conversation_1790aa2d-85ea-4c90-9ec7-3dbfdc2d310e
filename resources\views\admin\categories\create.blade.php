@extends('layouts.admin')

@section('title', 'Create Category')

@php
    $pageTitle = 'Create New Category';
    $pageDescription = 'Add a new category with SEO meta information';
    $breadcrumbs = [
        ['title' => 'Dashboard', 'url' => route('admin.dashboard')],
        ['title' => 'Categories', 'url' => route('admin.categories.index')],
        ['title' => 'Create', 'url' => '#'],
    ];
@endphp

@section('page-header')
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-semibold text-gray-900">{{ $pageTitle }}</h1>
            <p class="mt-2 text-gray-600">{{ $pageDescription }}</p>
        </div>
        <a href="{{ route('admin.categories.index') }}" class="material-button material-button-secondary flex items-center">
            <i class="material-icons mr-2">arrow_back</i>
            Back to Categories
        </a>
    </div>
@endsection

@section('content')
    <!-- Create Category Form -->
    <div class="material-card">
        <form method="POST" action="{{ route('admin.categories.store') }}" class="p-6" id="category_form" enctype="multipart/form-data">
            @csrf

            <!-- Basic Information Section -->
            <div class="space-y-6">
                <div class="border-b border-gray-200 pb-4">
                    <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                    <p class="mt-1 text-sm text-gray-600">Enter the basic category details</p>
                </div>

                <!-- Row 1: Name and Slug -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Name Field -->
                    <div class="order-1 lg:order-1">
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            Category Name <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">category</i>
                            </div>
                            <input id="name" name="name" type="text" required value="{{ old('name') }}"
                                class="material-input-with-icon @error('name') material-input-error @enderror"
                                placeholder="Enter category name" onkeyup="generateSlug()">
                        </div>
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Slug Field -->
                    <div class="order-2 lg:order-2">
                        <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">
                            URL Slug <small class="text-gray-500">(auto-generated)</small>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">link</i>
                            </div>
                            <input id="slug" name="slug" type="text" value="{{ old('slug') }}"
                                class="material-input-with-icon @error('slug') material-input-error @enderror"
                                placeholder="category-url-slug">
                        </div>
                        @error('slug')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Row 2: Color and Status -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Color Field -->
                    <div class="order-3 lg:order-1">
                        <label for="color" class="block text-sm font-medium text-gray-700 mb-2">
                            Color <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">palette</i>
                            </div>
                            <div class="flex items-center space-x-3">
                                <input type="color"
                                       name="color"
                                       id="color"
                                       value="{{ old('color', '#3B82F6') }}"
                                       class="w-12 h-10 border border-gray-300 rounded-md cursor-pointer @error('color') border-red-500 @enderror">
                                <input type="text"
                                       id="color_text"
                                       value="{{ old('color', '#3B82F6') }}"
                                       class="material-input flex-1 @error('color') border-red-500 @enderror"
                                       placeholder="#3B82F6"
                                       readonly>
                            </div>
                        </div>
                        @error('color')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Status Field -->
                    <div class="order-4 lg:order-2">
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                            Status <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">toggle_on</i>
                            </div>
                            <select id="status" name="status" required
                                class="material-select-with-icon @error('status') material-select-error @enderror">
                                <option value="active" {{ old('status', 'active') == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>
                        @error('status')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Row 3: Parent Category and Featured -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Parent Category Field -->
                    <div class="order-5 lg:order-1">
                        <label for="parent_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Parent Category <small class="text-gray-500">(optional)</small>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">account_tree</i>
                            </div>
                            <select id="parent_id" name="parent_id"
                                class="material-select-with-icon @error('parent_id') material-select-error @enderror">
                                <option value="">Select parent category</option>
                                @foreach ($parentCategories as $parentCategory)
                                    <option value="{{ $parentCategory->id }}" {{ old('parent_id') == $parentCategory->id ? 'selected' : '' }}>
                                        {{ $parentCategory->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        @error('parent_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Featured Field -->
                    <div class="order-6 lg:order-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Featured Category
                        </label>
                        <div class="flex items-center">
                            <input id="is_featured" name="is_featured" type="checkbox" value="1"
                                {{ old('is_featured') ? 'checked' : '' }}
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="is_featured" class="ml-2 block text-sm text-gray-900">
                                Mark as featured category
                            </label>
                        </div>
                        @error('is_featured')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Row 4: Sort Order -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Sort Order Field -->
                    <div class="order-7 lg:order-1">
                        <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">
                            Sort Order <small class="text-gray-500">(0 = first)</small>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">sort</i>
                            </div>
                            <input id="sort_order" name="sort_order" type="number" min="0" value="{{ old('sort_order', 0) }}"
                                class="material-input-with-icon @error('sort_order') material-input-error @enderror"
                                placeholder="0">
                        </div>
                        @error('sort_order')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Description Field -->
                <div class="grid grid-cols-1 gap-6">
                    <div class="order-7">
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                            Description <small class="text-gray-500">(optional)</small>
                        </label>
                        <div class="ckeditor-container">
                            <textarea id="description" name="description"
                                class="ckeditor @error('description') border-red-300 @enderror"
                                placeholder="Enter category description">{{ old('description') }}</textarea>
                        </div>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- SEO Meta Information Section -->
            <div class="space-y-6 mt-8">
                <div class="border-b border-gray-200 pb-4">
                    <h3 class="text-lg font-medium text-gray-900">SEO Meta Information</h3>
                    <p class="mt-1 text-sm text-gray-600">Configure SEO settings for better search engine visibility</p>
                </div>

                <!-- Meta Title and Meta Description -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Meta Title -->
                    <div>
                        <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-2">
                            Meta Title <small class="text-gray-500">(recommended: 50-60 chars)</small>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">title</i>
                            </div>
                            <input id="meta_title" name="meta_title" type="text" value="{{ old('meta_title') }}"
                                class="material-input-with-icon @error('meta_title') material-input-error @enderror"
                                placeholder="SEO title for search engines" maxlength="255">
                        </div>
                        <div class="mt-1 text-xs text-gray-500">
                            <span id="meta_title_count">0</span>/60 characters
                        </div>
                        @error('meta_title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Robots Meta -->
                    <div>
                        <label for="robots_meta" class="block text-sm font-medium text-gray-700 mb-2">
                            Robots Meta
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">smart_toy</i>
                            </div>
                            <select id="robots_meta" name="robots_meta"
                                class="material-select-with-icon @error('robots_meta') material-select-error @enderror">
                                <option value="index,follow" {{ old('robots_meta', 'index,follow') == 'index,follow' ? 'selected' : '' }}>Index, Follow</option>
                                <option value="index,nofollow" {{ old('robots_meta') == 'index,nofollow' ? 'selected' : '' }}>Index, No Follow</option>
                                <option value="noindex,follow" {{ old('robots_meta') == 'noindex,follow' ? 'selected' : '' }}>No Index, Follow</option>
                                <option value="noindex,nofollow" {{ old('robots_meta') == 'noindex,nofollow' ? 'selected' : '' }}>No Index, No Follow</option>
                            </select>
                        </div>
                        @error('robots_meta')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Meta Description -->
                <div class="grid grid-cols-1 gap-6">
                    <div>
                        <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">
                            Meta Description <small class="text-gray-500">(recommended: 150-160 chars)</small>
                        </label>
                        <textarea id="meta_description" name="meta_description" rows="3"
                            class="material-textarea @error('meta_description') material-textarea-error @enderror"
                            placeholder="Brief description for search engine results" maxlength="500">{{ old('meta_description') }}</textarea>
                        <div class="mt-1 text-xs text-gray-500">
                            <span id="meta_description_count">0</span>/160 characters
                        </div>
                        @error('meta_description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Meta Keywords and Canonical URL -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Meta Keywords -->
                    <div>
                        <label for="meta_keywords" class="block text-sm font-medium text-gray-700 mb-2">
                            Meta Keywords <small class="text-gray-500">(comma separated)</small>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">local_offer</i>
                            </div>
                            <input id="meta_keywords" name="meta_keywords" type="text" value="{{ old('meta_keywords') }}"
                                class="material-input-with-icon @error('meta_keywords') material-input-error @enderror"
                                placeholder="keyword1, keyword2, keyword3">
                        </div>
                        @error('meta_keywords')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Canonical URL -->
                    <div>
                        <label for="canonical_url" class="block text-sm font-medium text-gray-700 mb-2">
                            Canonical URL <small class="text-gray-500">(optional)</small>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">link</i>
                            </div>
                            <input id="canonical_url" name="canonical_url" type="url" value="{{ old('canonical_url') }}"
                                class="material-input-with-icon @error('canonical_url') material-input-error @enderror"
                                placeholder="https://example.com/canonical-url">
                        </div>
                        @error('canonical_url')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Open Graph Information Section -->
            <div class="space-y-6 mt-8">
                <div class="border-b border-gray-200 pb-4">
                    <h3 class="text-lg font-medium text-gray-900">Open Graph (Social Media)</h3>
                    <p class="mt-1 text-sm text-gray-600">Configure how this category appears when shared on social media</p>
                </div>

                <!-- OG Title and OG Type -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- OG Title -->
                    <div>
                        <label for="og_title" class="block text-sm font-medium text-gray-700 mb-2">
                            OG Title <small class="text-gray-500">(falls back to meta title)</small>
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">share</i>
                            </div>
                            <input id="og_title" name="og_title" type="text" value="{{ old('og_title') }}"
                                class="material-input-with-icon @error('og_title') material-input-error @enderror"
                                placeholder="Title for social media sharing" maxlength="255">
                        </div>
                        @error('og_title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- OG Type -->
                    <div>
                        <label for="og_type" class="block text-sm font-medium text-gray-700 mb-2">
                            OG Type
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                <i class="material-icons text-gray-400 text-lg">category</i>
                            </div>
                            <select id="og_type" name="og_type"
                                class="material-select-with-icon @error('og_type') material-select-error @enderror">
                                <option value="website" {{ old('og_type', 'website') == 'website' ? 'selected' : '' }}>Website</option>
                                <option value="article" {{ old('og_type') == 'article' ? 'selected' : '' }}>Article</option>
                                <option value="product" {{ old('og_type') == 'product' ? 'selected' : '' }}>Product</option>
                            </select>
                        </div>
                        @error('og_type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- OG Description -->
                <div class="grid grid-cols-1 gap-6">
                    <div>
                        <label for="og_description" class="block text-sm font-medium text-gray-700 mb-2">
                            OG Description <small class="text-gray-500">(falls back to meta description)</small>
                        </label>
                        <textarea id="og_description" name="og_description" rows="3"
                            class="material-textarea @error('og_description') material-textarea-error @enderror"
                            placeholder="Description for social media sharing" maxlength="500">{{ old('og_description') }}</textarea>
                        @error('og_description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- OG Image -->
                <div class="grid grid-cols-1 gap-6">
                    <div>
                        <label for="og_image" class="block text-sm font-medium text-gray-700 mb-2">
                            OG Image <small class="text-gray-500">(recommended: 1200x630px)</small>
                        </label>
                        <div class="mt-1 flex items-center space-x-4">
                            <!-- Image Preview -->
                            <div class="flex-shrink-0">
                                <img id="og-image-preview"
                                    class="h-20 w-32 rounded-lg object-cover border-2 border-gray-300 bg-gray-100"
                                    src="data:image/svg+xml,%3csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100' height='100' fill='%23f3f4f6'/%3e%3ctext x='50%25' y='50%25' font-size='14' text-anchor='middle' dy='.3em' fill='%236b7280'%3eNo Image%3c/text%3e%3c/svg%3e"
                                    alt="OG image preview">
                            </div>
                            <!-- File Input -->
                            <div class="flex-1">
                                <div class="relative">
                                    <input id="og_image" name="og_image" type="file" accept="image/*"
                                        class="hidden" onchange="previewOgImage(this)">
                                    <label for="og_image"
                                        class="material-button material-button-sm material-button-secondary cursor-pointer flex items-center">
                                        <i class="material-icons mr-2 text-sm">upload</i>
                                        Choose OG Image
                                    </label>
                                </div>
                                <p class="mt-1 text-xs text-gray-500">PNG, JPG, GIF up to 2MB. Recommended: 1200x630px</p>
                            </div>
                        </div>
                        @error('og_image')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 mt-8">
                <a href="{{ route('admin.categories.index') }}"
                    class="material-button material-button-sm material-button-secondary flex items-center">
                    Cancel
                </a>
                <button type="submit"
                    class="material-button material-button-sm material-button-primary flex items-center">
                    <i class="material-icons mr-2 text-sm">save</i>
                    Create Category
                </button>
            </div>
        </form>
    </div>
@endsection

@push('styles')
<!-- CKEditor 5 Styles -->
<style>
    .ck-editor__editable {
        min-height: 200px;
    }
    .ck.ck-editor {
        max-width: 100%;
    }
    .ck-content {
        font-family: inherit;
    }
</style>
@endpush

@push('scripts')
    @vite('resources/js/utils/common.js')
    <script src="https://code.jquery.com/jquery-3.7.0.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
    <!-- CKEditor 5 -->
    <script src="https://cdn.ckeditor.com/ckeditor5/40.2.0/classic/ckeditor.js"></script>
    <script>
        // Initialize CKEditor
        let descriptionEditor;

        ClassicEditor
            .create(document.querySelector('#description'), {
                toolbar: {
                    items: [
                        'heading', '|',
                        'bold', 'italic', 'underline', 'strikethrough', '|',
                        'fontSize', 'fontColor', 'fontBackgroundColor', '|',
                        'alignment', '|',
                        'numberedList', 'bulletedList', '|',
                        'outdent', 'indent', '|',
                        'link', 'blockQuote', 'insertTable', '|',
                        'undo', 'redo', '|',
                        'sourceEditing'
                    ]
                },
                heading: {
                    options: [
                        { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                        { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                        { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                        { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' },
                        { model: 'heading4', view: 'h4', title: 'Heading 4', class: 'ck-heading_heading4' }
                    ]
                },
                fontSize: {
                    options: [
                        'tiny', 'small', 'default', 'big', 'huge'
                    ]
                },
                fontColor: {
                    colors: [
                        {
                            color: 'hsl(0, 0%, 0%)',
                            label: 'Black'
                        },
                        {
                            color: 'hsl(0, 0%, 30%)',
                            label: 'Dim grey'
                        },
                        {
                            color: 'hsl(0, 0%, 60%)',
                            label: 'Grey'
                        },
                        {
                            color: 'hsl(0, 0%, 90%)',
                            label: 'Light grey'
                        },
                        {
                            color: 'hsl(0, 0%, 100%)',
                            label: 'White',
                            hasBorder: true
                        },
                        {
                            color: 'hsl(0, 75%, 60%)',
                            label: 'Red'
                        },
                        {
                            color: 'hsl(30, 75%, 60%)',
                            label: 'Orange'
                        },
                        {
                            color: 'hsl(60, 75%, 60%)',
                            label: 'Yellow'
                        },
                        {
                            color: 'hsl(90, 75%, 60%)',
                            label: 'Light green'
                        },
                        {
                            color: 'hsl(120, 75%, 60%)',
                            label: 'Green'
                        },
                        {
                            color: 'hsl(150, 75%, 60%)',
                            label: 'Aquamarine'
                        },
                        {
                            color: 'hsl(180, 75%, 60%)',
                            label: 'Turquoise'
                        },
                        {
                            color: 'hsl(210, 75%, 60%)',
                            label: 'Light blue'
                        },
                        {
                            color: 'hsl(240, 75%, 60%)',
                            label: 'Blue'
                        },
                        {
                            color: 'hsl(270, 75%, 60%)',
                            label: 'Purple'
                        }
                    ]
                },
                fontBackgroundColor: {
                    colors: [
                        {
                            color: 'hsl(0, 0%, 100%)',
                            label: 'White',
                            hasBorder: true
                        },
                        {
                            color: 'hsl(0, 0%, 90%)',
                            label: 'Light grey'
                        },
                        {
                            color: 'hsl(60, 75%, 90%)',
                            label: 'Light yellow'
                        },
                        {
                            color: 'hsl(120, 75%, 90%)',
                            label: 'Light green'
                        },
                        {
                            color: 'hsl(180, 75%, 90%)',
                            label: 'Light cyan'
                        },
                        {
                            color: 'hsl(240, 75%, 90%)',
                            label: 'Light blue'
                        },
                        {
                            color: 'hsl(300, 75%, 90%)',
                            label: 'Light purple'
                        }
                    ]
                },
                table: {
                    contentToolbar: [
                        'tableColumn',
                        'tableRow',
                        'mergeTableCells'
                    ]
                },
                link: {
                    decorators: {
                        openInNewTab: {
                            mode: 'manual',
                            label: 'Open in a new tab',
                            attributes: {
                                target: '_blank',
                                rel: 'noopener noreferrer'
                            }
                        }
                    }
                }
            })
            .then(editor => {
                descriptionEditor = editor;

                // Sync with form validation
                editor.model.document.on('change:data', () => {
                    const data = editor.getData();
                    document.querySelector('#description').value = data;

                    // Trigger validation if using jQuery validation
                    if (typeof $.validator !== 'undefined') {
                        $('#description').valid();
                    }
                });
            })
            .catch(error => {
                console.error('CKEditor initialization error:', error);
            });

        $(document).ready(function() {
            // Color picker sync
            $('#color').on('input', function() {
                $('#color_text').val($(this).val());
            });

            $('#color_text').on('input', function() {
                let color = $(this).val();
                if (/^#[0-9A-F]{6}$/i.test(color)) {
                    $('#color').val(color);
                }
            });

            // Character counters
            $('#meta_title').on('input', function() {
                const count = $(this).val().length;
                $('#meta_title_count').text(count);
                $(this).toggleClass('border-yellow-500', count > 60);
            });

            $('#meta_description').on('input', function() {
                const count = $(this).val().length;
                $('#meta_description_count').text(count);
                $(this).toggleClass('border-yellow-500', count > 160);
            });

            // Custom validation method for hex color
            $.validator.addMethod("hexcolor", function(value, element) {
                return this.optional(element) || /^#[0-9A-Fa-f]{6}$/i.test(value);
            }, "Please enter a valid hex color code (e.g., #FF0000)");

            // jQuery Form validation
            $("#category_form").validate({
                rules: {
                    name: {
                        required: true,
                        minlength: 2,
                        maxlength: 255
                    },
                    slug: {
                        maxlength: 255
                    },
                    color: {
                        required: true,
                        hexcolor: true
                    },
                    status: {
                        required: true
                    },
                    parent_id: {
                        number: true
                    },
                    sort_order: {
                        number: true,
                        min: 0
                    },
                    meta_title: {
                        maxlength: 255
                    },
                    meta_description: {
                        maxlength: 500
                    },
                    canonical_url: {
                        url: true
                    },
                    og_title: {
                        maxlength: 255
                    },
                    og_description: {
                        maxlength: 500
                    },
                    og_image: {
                        extension: "jpg|jpeg|png|gif",
                        filesize: 2097152 // 2MB in bytes
                    }
                },
                messages: {
                    name: {
                        required: "Please enter the category name",
                        minlength: "Category name must be at least 2 characters",
                        maxlength: "Category name cannot exceed 255 characters"
                    },
                    slug: {
                        maxlength: "Slug cannot exceed 255 characters"
                    },
                    color: {
                        required: "Please select a color",
                        hexcolor: "Please enter a valid hex color code"
                    },
                    status: {
                        required: "Please select a status"
                    },
                    sort_order: {
                        number: "Sort order must be a number",
                        min: "Sort order cannot be negative"
                    },
                    meta_title: {
                        maxlength: "Meta title cannot exceed 255 characters"
                    },
                    meta_description: {
                        maxlength: "Meta description cannot exceed 500 characters"
                    },
                    canonical_url: {
                        url: "Please enter a valid URL"
                    },
                    og_title: {
                        maxlength: "OG title cannot exceed 255 characters"
                    },
                    og_description: {
                        maxlength: "OG description cannot exceed 500 characters"
                    },
                    og_image: {
                        extension: "Please select a valid image file (JPG, JPEG, PNG, GIF)",
                        filesize: "Image file size must be less than 2MB"
                    }
                },
                errorElement: 'span',
                errorPlacement: function (error, element) {
                    error.addClass("text-red-500 text-xs mt-1").insertAfter(element.closest('.relative'));
                },
                highlight: function (element) {
                    $(element).addClass('border-red-500').removeClass('border-gray-300');
                },
                unhighlight: function (element) {
                    $(element).removeClass('border-red-500').addClass('border-gray-300');
                }
            });

            // Custom validation methods
            $.validator.addMethod("filesize", function(value, element, param) {
                return this.optional(element) || (element.files[0] && element.files[0].size <= param);
            }, "File size must be less than {0} bytes");

            // Form submission handler
            $('#category_form').on('submit', function(e) {
                // Ensure CKEditor data is synced before submission
                if (descriptionEditor) {
                    const editorData = descriptionEditor.getData();
                    $('#description').val(editorData);
                }
            });
        });

        // Generate slug from name
        function generateSlug() {
            const name = document.getElementById('name').value;
            const slug = name.toLowerCase()
                .replace(/[^a-z0-9 -]/g, '') // Remove invalid chars
                .replace(/\s+/g, '-') // Replace spaces with -
                .replace(/-+/g, '-') // Replace multiple - with single -
                .trim('-'); // Trim - from start and end

            document.getElementById('slug').value = slug;
        }

        // Preview OG image
        function previewOgImage(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('og-image-preview').src = e.target.result;
                };
                reader.readAsDataURL(input.files[0]);
            }
        }
    </script>
@endpush
