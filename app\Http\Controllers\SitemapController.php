<?php

namespace App\Http\Controllers;

use Illuminate\Http\Response;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Route;
use App\Models\News;
use App\Models\Category;
use App\Models\Tag;

class SitemapController extends Controller
{
    protected static int $chunkSize;

    public function __construct()
    {
        self::$chunkSize = (int) env('SITEMAP_CHUNK_SIZE', 1000);
    }
    // Sitemap index
    public function index()
    {
        $types = ['news', 'categories', 'tags'];
        $sitemaps = [];
        $sitemaps[] = [
            'loc' => url('/'),
            'changefreq' => 'always',
            'priority' => '1.0',
            'lastmod' => now()->toAtomString(), // Set lastmod to now or homepage updated time
        ];
        // Generate sitemap chunk entries
        foreach ($types as $type) {
            $model = $this->getModel($type);
            $count = $model::count();
            $chunks = ceil($count / self::$chunkSize);

            for ($i = 1; $i <= $chunks; $i++) {
                // Optionally get last updated item in chunk range
                $lastItem = $model::select('updated_at')
                    ->skip(($i - 1) * self::$chunkSize)
                    ->take(self::$chunkSize)
                    ->orderByDesc('updated_at')
                    ->first();

                $sitemaps[] = [
                    'loc' => url("/sitemap_{$type}_{$i}.xml"),
                    'lastmod' => optional($lastItem)->updated_at?->toAtomString() ?? now()->toAtomString(),
                ];
            }
        }
        $xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        $xml .= view('sitemap.index', ['sitemaps' => $sitemaps])->render();

        return response($xml, 200)->header('Content-Type', 'application/xml');
    }

    // Individual sitemap chunks
    public function chunked($type, $chunk)
    {
        $model = $this->getModel($type);

        $items = $model::select('slug', 'updated_at')
            ->skip(($chunk - 1) * self::$chunkSize)
            ->take(self::$chunkSize)
            ->get();

        $urls = $items->map(function ($item) use ($type) {
            $urlPrefix = match ($type) {
                'news' => 'news',
                'categories' => 'category',
                'tags' => 'tag',
            };

            $changefreq = match ($type) {
                'news' => 'hourly',
                'categories' => 'daily',
                'tags' => 'daily',
                default => 'daily',
            };
            $priority = match ($type) {
                'news' => '0.9',
                'categories' => '0.8',
                'tags' => '0.8',
                default => '1.0',
            };

            return [
                'loc' => url("/{$urlPrefix}/" . $item->slug),
                'lastmod' => $item->updated_at->toAtomString(),
                'changefreq' => $changefreq,
                'priority' => $priority,
            ];
        });

        $xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        $xml .= view('sitemap.chunk', ['urls' => $urls])->render();

        return response($xml, 200)->header('Content-Type', 'application/xml');
    }

    private function getModel($type)
    {
        return match ($type) {
            'news' => News::class,
            // 'pages' => Page::class,
            'categories' => Category::class,
            'tags' => Tag::class,
            default => abort(404)
        };
    }

    public function robots()
    {
        $content = "User-agent: *\n";
        $content .= "Disallow: /admin\n";
        $content .= "Disallow: /login\n";
        $content .= "Disallow: /register\n";
        $content .= "Disallow: /password/reset\n";
        $content .= "Sitemap: " . url('/sitemap.xml') . "\n";

        return response($content, 200)->header('Content-Type', 'text/plain');
    }
}
