/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
        './resources/js/**/*.js',
    ],

    darkMode: 'class', // Enable class-based dark mode
    
    theme: {
        extend: {
            typography: {
                DEFAULT: {
                    css: {
                        color: 'var(--color-text-primary)',
                        a: {
                            color: 'var(--color-accent)',
                            '&:hover': {
                                color: 'var(--color-accent-hover)',
                            },
                        },
                    },
                },
            },
        },
    },
    
    plugins: [
        require('@tailwindcss/typography'),
        require('@tailwindcss/forms'),
        require('@tailwindcss/aspect-ratio'),
    ],
};

