@foreach($articles as $news)
<article class="py-0 flex">
    <a class="article-image w-2/5 flex-shrink-0" href="{{ route('news.show', $news->slug) }}">
        <div class="group aspect-h-9 aspect-w-16 overflow-hidden rounded bg-gray-100 dark:bg-gray-800 relative">
            <img alt="{{ $news->title }}"
                loading="lazy"
                decoding="async"
                class="rounded object-cover object-center transition duration-300 ease-in-out group-hover:scale-110"
                style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent"
                src="{{ asset('storage/' . $news->main_image) }}"
                onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
            <!-- Image placeholder -->
            <div class="absolute inset-0 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center" style="display:none;">
                <svg class="h-8 w-8 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
            </div>
        </div>
    </a>
    <div class="ml-3 flex-1">
        @if($news->categories->isNotEmpty())
        <a class="text-xs font-medium uppercase tracking-widest transition-colors duration-300 hover:opacity-90"
            href="{{ route('category.show', $news->categories->first()->slug) }}"
            style="color: <?= $news->categories->first()->color ?>;">
            {{ $news->categories->first()->name }}
        </a>
        @endif
        <a href="{{ route('news.show', $news->slug) }}">
            <h3 class="text-sm lg:text-sm font-medium leading-normal tracking-normal text-gray-900 dark:text-white decoration-gray-800 dark:decoration-gray-200 decoration-2 transition duration-300 ease-in-out">
                {{ $news->title }}
            </h3>
        </a>
        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
            {{ $news->published_at->format('M d, Y') }}
        </p>
    </div>
</article>
@endforeach