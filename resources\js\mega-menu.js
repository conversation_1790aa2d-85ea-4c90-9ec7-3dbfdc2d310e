// Mega Menu JavaScript
class MegaMenu {
    constructor() {
        this.mobileMenuOpen = false;
        this.init();
    }

    init() {
        this.initMobileMenu();
        this.initMegaMenus();
        this.initMobileSections();
        this.initResizeHandler();
    }

    initResizeHandler() {
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.handleResize();
            }, 100);
        });
    }

    initMobileMenu() {
        const mobileButton = document.getElementById('mobile-nav-button');
        const mobileMenu = document.getElementById('mobile-slide-menu');
        const mobileOverlay = document.getElementById('mobile-menu-overlay');
        const mobileClose = document.getElementById('mobile-menu-close');

        if (!mobileButton || !mobileMenu || !mobileOverlay) return;

        // Toggle mobile menu
        mobileButton.addEventListener('click', () => {
            this.toggleMobileMenu();
        });

        // Close mobile menu
        mobileClose?.addEventListener('click', () => {
            this.closeMobileMenu();
        });

        // Close on overlay click
        mobileOverlay.addEventListener('click', () => {
            this.closeMobileMenu();
        });

        // Close on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.mobileMenuOpen) {
                this.closeMobileMenu();
            }
        });

        // Animate hamburger menu
        this.initHamburgerAnimation();
    }

    initHamburgerAnimation() {
        const mobileButton = document.getElementById('mobile-nav-button');
        const lines = [
            document.getElementById('menu-line-1'),
            document.getElementById('menu-line-2'),
            document.getElementById('menu-line-3'),
            document.getElementById('menu-line-4')
        ];

        if (!mobileButton || lines.some(line => !line)) return;

        mobileButton.addEventListener('click', () => {
            if (this.mobileMenuOpen) {
                // Close animation
                lines[0].style.transform = 'rotate(0deg) translateY(0px)';
                lines[1].style.opacity = '1';
                lines[2].style.opacity = '1';
                lines[3].style.transform = 'rotate(0deg) translateY(0px)';
            } else {
                // Open animation
                lines[0].style.transform = 'rotate(45deg) translateY(6px)';
                lines[1].style.opacity = '0';
                lines[2].style.opacity = '0';
                lines[3].style.transform = 'rotate(-45deg) translateY(-6px)';
            }
        });
    }

    toggleMobileMenu() {
        if (this.mobileMenuOpen) {
            this.closeMobileMenu();
        } else {
            this.openMobileMenu();
        }
    }

    openMobileMenu() {
        const mobileMenu = document.getElementById('mobile-slide-menu');
        const mobileOverlay = document.getElementById('mobile-menu-overlay');
        
        if (!mobileMenu || !mobileOverlay) return;

        this.mobileMenuOpen = true;
        // document.body.style.overflow = 'hidden';
        
        mobileOverlay.classList.remove('opacity-0', 'pointer-events-none');
        mobileOverlay.classList.add('opacity-100');
        
        mobileMenu.classList.remove('-translate-x-full');
        mobileMenu.classList.add('translate-x-0');
    }

    closeMobileMenu() {
        const mobileMenu = document.getElementById('mobile-slide-menu');
        const mobileOverlay = document.getElementById('mobile-menu-overlay');
        
        if (!mobileMenu || !mobileOverlay) return;

        this.mobileMenuOpen = false;
        // document.body.style.overflow = '';
        
        mobileOverlay.classList.remove('opacity-100');
        mobileOverlay.classList.add('opacity-0', 'pointer-events-none');
        
        mobileMenu.classList.remove('translate-x-0');
        mobileMenu.classList.add('-translate-x-full');
    }

    initMegaMenus() {
        const megaMenus = document.querySelectorAll('[data-mega-menu]');
        
        megaMenus.forEach(menu => {
            const trigger = menu.querySelector('[data-mega-trigger]');
            const content = menu.querySelector('[data-mega-content]');
            
            if (!trigger || !content) return;

            let hoverTimeout;

            // Show mega menu on hover
            menu.addEventListener('mouseenter', () => {
                clearTimeout(hoverTimeout);
                this.showMegaMenu(content);
            });

            // Hide mega menu on leave with delay
            menu.addEventListener('mouseleave', () => {
                hoverTimeout = setTimeout(() => {
                    this.hideMegaMenu(content);
                }, 150);
            });

            // Keyboard navigation
            trigger.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.toggleMegaMenu(content);
                }
            });
        });

        // Close mega menus when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('[data-mega-menu]')) {
                this.hideAllMegaMenus();
            }
        });
    }

    showMegaMenu(content) {
        // Adjust positioning to prevent overflow
        this.adjustMegaMenuPosition(content);

        content.classList.remove('opacity-0', 'invisible');
        content.classList.add('opacity-100', 'visible');
        content.style.transform = 'translateX(-50%) translateY(0)';
    }

    hideMegaMenu(content) {
        content.classList.remove('opacity-100', 'visible');
        content.classList.add('opacity-0', 'invisible');
        content.style.transform = 'translateX(-50%) translateY(8px)';
    }

    adjustMegaMenuPosition(content) {
        // Get viewport width
        const viewportWidth = window.innerWidth;
        const contentRect = content.getBoundingClientRect();
        const parentRect = content.parentElement.getBoundingClientRect();

        // Calculate if the menu would overflow
        const menuWidth = 800; // Default width
        const leftEdge = parentRect.left + (parentRect.width / 2) - (menuWidth / 2);
        const rightEdge = leftEdge + menuWidth;

        // Reset any previous adjustments
        content.style.left = '50%';
        content.style.right = 'auto';

        // Adjust if overflowing right edge
        if (rightEdge > viewportWidth - 16) {
            content.style.left = 'auto';
            content.style.right = '0';
            content.style.transform = 'translateX(0) translateY(8px)';
        }
        // Adjust if overflowing left edge
        else if (leftEdge < 16) {
            content.style.left = '0';
            content.style.transform = 'translateX(0) translateY(8px)';
        }
    }

    toggleMegaMenu(content) {
        if (content.classList.contains('opacity-100')) {
            this.hideMegaMenu(content);
        } else {
            this.hideAllMegaMenus();
            this.showMegaMenu(content);
        }
    }

    // Add window resize handler to readjust positioning
    handleResize() {
        const visibleMenus = document.querySelectorAll('[data-mega-content].opacity-100');
        visibleMenus.forEach(menu => {
            this.adjustMegaMenuPosition(menu);
        });
    }

    hideAllMegaMenus() {
        const allMegaMenus = document.querySelectorAll('[data-mega-content]');
        allMegaMenus.forEach(menu => {
            this.hideMegaMenu(menu);
        });
    }

    initMobileSections() {
        const sectionToggles = document.querySelectorAll('[data-section]');

        sectionToggles.forEach(toggle => {
            toggle.addEventListener('click', () => {
                const sectionName = toggle.getAttribute('data-section');
                const content = document.querySelector(`[data-content="${sectionName}"]`);
                const arrow = toggle.querySelector('svg:last-child'); // Get the arrow SVG

                if (!content || !arrow) return;

                const isActive = content.classList.contains('active');

                // Close all other sections
                this.closeAllMobileSections();

                if (!isActive) {
                    // Open this section
                    content.classList.add('active');
                    arrow.classList.add('rotated');
                    toggle.classList.add('active');
                }
            });
        });
    }

    closeAllMobileSections() {
        const allContents = document.querySelectorAll('[data-content]');
        const allArrows = document.querySelectorAll('[data-section] svg:last-child');
        const allToggles = document.querySelectorAll('[data-section]');

        allContents.forEach(content => {
            content.classList.remove('active');
        });

        allArrows.forEach(arrow => {
            arrow.classList.remove('rotated');
        });

        allToggles.forEach(toggle => {
            toggle.classList.remove('active');
        });
    }
}

// Initialize mega menu when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new MegaMenu();
});

// Export for use in other modules
export default MegaMenu;
