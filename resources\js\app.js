import './bootstrap';
import { initTheme } from './theme';
import './mega-menu';
import Swiper from 'swiper';
import { Autoplay, FreeMode } from 'swiper/modules';
import 'swiper/css';

// Initialize Swiper globally
window.Swiper = Swiper;
window.SwiperModules = { Autoplay, FreeMode };

document.addEventListener('DOMContentLoaded', function () {
    // Other existing code...
    initTheme();
    
    // Initialize category slider if it exists
    initCategorySlider();
});

function initCategorySlider() {
    const sliderEl = document.querySelector('.categories-slider');
    if (!sliderEl) return;
    
    const categoriesSwiper = new Swiper('.categories-slider', {
        modules: [Autoplay, FreeMode],
        slidesPerView: 'auto',
        spaceBetween: 10,
        loop: true,
        autoplay: {
            delay: 0,
            disableOnInteraction: false,
        },
        speed: 5000,
        freeMode: true,
        freeModeMomentum: false,
    });
    
    // Pause on hover
    sliderEl.addEventListener('mouseenter', function() {
        categoriesSwiper.autoplay.stop();
    });
    
    sliderEl.addEventListener('mouseleave', function() {
        categoriesSwiper.autoplay.start();
    });
}

